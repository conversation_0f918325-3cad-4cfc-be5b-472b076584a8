package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GameHandler 游戏处理器
type GameHandler struct {
	worldService     *game.WorldService
	characterService *game.CharacterService
	sceneService     *game.SceneService
	eventService     *game.EventService
	stateService     *game.StateService
	aiService        *ai.Service
	logger           logger.Logger
}

// NewGameHandler 创建游戏处理器
func NewGameHandler(
	worldService *game.WorldService,
	characterService *game.CharacterService,
	sceneService *game.SceneService,
	eventService *game.EventService,
	stateService *game.StateService,
	aiService *ai.Service,
) *GameHandler {
	return &GameHandler{
		worldService:     worldService,
		characterService: characterService,
		sceneService:     sceneService,
		eventService:     eventService,
		stateService:     stateService,
		aiService:        aiService,
		logger:           logger.New("game-handler"),
	}
}

// CreateWorld 创建世界
// @Summary 创建新世界
// @Description 创建一个新的游戏世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateWorldRequest true "创建世界请求"
// @Success 200 {object} Response{data=models.World}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds [post]
func (h *GameHandler) CreateWorld(c *gin.Context) {
	var req CreateWorldRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 构建世界配置
	worldConfig := make(map[string]interface{})
	worldConfig["theme"] = req.Theme
	worldConfig["is_public"] = req.IsPublic
	worldConfig["max_players"] = req.MaxPlayers

	// 合并用户提供的世界设置
	if req.WorldSettings != nil {
		for key, value := range req.WorldSettings {
			worldConfig[key] = value
		}
	}

	world, err := h.worldService.CreateWorld(c.Request.Context(), userID.String(), req.Name, req.Description, worldConfig)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建世界失败",
			Error:   err.Error(),
		})
		return
	}

	// 异步生成初始内容（场景和NPC）
	go h.generateInitialWorldContent(world, req)

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建世界成功",
		Data:    world,
	})
}

// GenerateWorlds AI生成世界配置
// @Summary AI生成世界配置
// @Description 使用AI生成多个候选世界配置供用户选择
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body GenerateWorldsRequest true "生成世界配置请求"
// @Success 200 {object} Response{data=GenerateWorldsResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/generate [post]
func (h *GameHandler) GenerateWorlds(c *gin.Context) {
	var req GenerateWorldsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	h.logger.Info("开始AI生成世界配置",
		"user_id", userID.String(),
		"world_name", req.WorldName,
		"settings_count", len(req.WorldSettings))

	// 生成多个候选世界配置
	candidates, err := h.generateWorldCandidates(c.Request.Context(), req, userID.String())
	if err != nil {
		h.logger.Error("AI生成世界配置失败", "error", err, "user_id", userID.String())
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成世界配置失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("AI生成世界配置成功",
		"user_id", userID.String(),
		"candidates_count", len(candidates))

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成世界配置成功",
		Data: GenerateWorldsResponse{
			Candidates: candidates,
		},
	})
}

// GetWorld 获取世界信息
// @Summary 获取世界信息
// @Description 获取指定世界的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response{data=models.World}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/worlds/{world_id} [get]
func (h *GameHandler) GetWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界信息成功",
		Data:    world,
	})
}

// GetMyWorlds 获取我的世界列表
// @Summary 获取我的世界列表
// @Description 获取当前用户创建的世界列表
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 401 {object} Response
// @Router /game/my-worlds [get]
func (h *GameHandler) GetMyWorlds(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	worlds, total, err := h.worldService.GetWorldsByCreator(c.Request.Context(), userID.String(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界列表成功",
		Data: PaginatedResponse{
			Items:      worlds,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// GetPublicWorlds 获取公开世界列表
// @Summary 获取公开世界列表
// @Description 获取所有公开的世界列表
// @Tags Game
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Router /game/public-worlds [get]
func (h *GameHandler) GetPublicWorlds(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	worlds, total, err := h.worldService.GetPublicWorlds(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取公开世界列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取公开世界列表成功",
		Data: PaginatedResponse{
			Items:      worlds,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// JoinWorld 加入世界
// @Summary 加入世界
// @Description 用户加入指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/join [post]
func (h *GameHandler) JoinWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.JoinWorld(c.Request.Context(), worldID.String(), userID.String()); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "加入世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "成功加入世界",
	})
}

// LeaveWorld 离开世界
// @Summary 离开世界
// @Description 用户离开指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/leave [post]
func (h *GameHandler) LeaveWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.LeaveWorld(c.Request.Context(), worldID.String(), userID.String()); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "离开世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "成功离开世界",
	})
}

// CreateCharacter 创建角色
// @Summary 创建角色
// @Description 在指定世界中创建新角色
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateCharacterRequest true "创建角色请求"
// @Success 200 {object} Response{data=models.Character}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters [post]
func (h *GameHandler) CreateCharacter(c *gin.Context) {
	var req CreateCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	userIDStr := userID.String()
	character, err := h.characterService.CreateCharacter(
		c.Request.Context(),
		req.WorldID.String(),
		&userIDStr,
		req.Name,
		req.Description,
		req.CharacterType,
		req.Traits,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建角色成功",
		Data:    character,
	})
}

// GetCharacter 获取角色信息
// @Summary 获取角色信息
// @Description 获取指定角色的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Success 200 {object} Response{data=models.Character}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/characters/{character_id} [get]
func (h *GameHandler) GetCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	character, err := h.characterService.GetCharacter(c.Request.Context(), characterID.String())
	if err != nil {
		if err.Error() == "角色不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取角色信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取角色信息成功",
		Data:    character,
	})
}

// GetMyCharacters 获取我的角色列表
// @Summary 获取我的角色列表
// @Description 获取当前用户的角色列表
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 401 {object} Response
// @Router /game/my-characters [get]
func (h *GameHandler) GetMyCharacters(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	characters, total, err := h.characterService.GetCharactersByUser(c.Request.Context(), userID.String(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取角色列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取角色列表成功",
		Data: PaginatedResponse{
			Items:      characters,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// GetWorldCharacters 获取世界中的角色列表
// @Summary 获取世界中的角色列表
// @Description 获取指定世界中的角色列表，支持按角色类型筛选
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param character_type query string false "角色类型" Enums(player, npc)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/characters [get]
func (h *GameHandler) GetWorldCharacters(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	characterType := c.Query("character_type")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	// 验证角色类型参数
	if characterType != "" && characterType != "player" && characterType != "npc" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色类型，只支持 player 或 npc",
		})
		return
	}

	// 首先检查世界是否存在
	_, err = h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界信息失败",
			Error:   err.Error(),
		})
		return
	}

	characters, total, err := h.characterService.GetCharactersByWorld(c.Request.Context(), worldID.String(), characterType, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界角色列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界角色列表成功",
		Data: PaginatedResponse{
			Items:      characters,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// 请求结构体定义

// CreateWorldRequest 创建世界请求
type CreateWorldRequest struct {
	Name          string                 `json:"name" binding:"required"`          // 世界名称，必填，用于标识和显示世界
	Description   string                 `json:"description" binding:"required"`   // 世界描述，必填，详细描述世界的背景和设定
	Theme         string                 `json:"theme" binding:"required"`         // 世界主题，必填，如"奇幻"、"科幻"、"现代"等
	IsPublic      bool                   `json:"is_public"`                        // 是否公开，可选，决定其他用户是否可以查看和加入
	MaxPlayers    int                    `json:"max_players" binding:"min=1,max=100"` // 最大玩家数，必填，限制1-100人
	WorldSettings map[string]interface{} `json:"world_settings"`                   // 世界设置，可选，包含各种自定义配置参数
}

// GenerateWorldsRequest AI生成世界配置请求
type GenerateWorldsRequest struct {
	WorldName     string                 `json:"worldName" binding:"required,min=1,max=100"` // 世界名称，必填，长度1-100字符，用于AI生成世界配置
	WorldSettings map[string]interface{} `json:"worldSettings"`                              // 世界设置，可选，提供给AI的额外配置参数和偏好设置
}

// WorldConfiguration 世界配置结构
type WorldConfiguration struct {
	WorldDescription string                 `json:"worldDescription"`
	WorldRules       []WorldRule            `json:"worldRules,omitempty"`
	Environment      *EnvironmentConfig     `json:"environment,omitempty"`
	Culture          *CultureConfig         `json:"culture,omitempty"`
	History          *HistoryConfig         `json:"history,omitempty"`
	Geography        *GeographyConfig       `json:"geography,omitempty"`
}

// WorldRule 世界规则
type WorldRule struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
	Severity    string `json:"severity"`
	Enforcement string `json:"enforcement"`
}

// EnvironmentConfig 环境配置
type EnvironmentConfig struct {
	Climate   *ClimateConfig   `json:"climate,omitempty"`
	Terrain   *TerrainConfig   `json:"terrain,omitempty"`
	Resources *ResourcesConfig `json:"resources,omitempty"`
}

// ClimateConfig 气候配置
type ClimateConfig struct {
	Type            string   `json:"type"`
	Seasons         []string `json:"seasons"`
	WeatherPatterns []string `json:"weatherPatterns"`
}

// TerrainConfig 地形配置
type TerrainConfig struct {
	PrimaryTerrain string     `json:"primaryTerrain"`
	Landmarks      []Landmark `json:"landmarks,omitempty"`
}

// Landmark 地标
type Landmark struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description,omitempty"`
}

// ResourcesConfig 资源配置
type ResourcesConfig struct {
	CommonResources []string       `json:"commonResources,omitempty"`
	RareResources   []RareResource `json:"rareResources,omitempty"`
}

// RareResource 稀有资源
type RareResource struct {
	Name        string `json:"name"`
	Rarity      string `json:"rarity"`
	Description string `json:"description,omitempty"`
}

// CultureConfig 文化配置
type CultureConfig struct {
	Societies  []Society   `json:"societies,omitempty"`
	Languages  []Language  `json:"languages,omitempty"`
	Traditions []Tradition `json:"traditions,omitempty"`
}

// Society 社会群体
type Society struct {
	Name        string            `json:"name"`
	Type        string            `json:"type"`
	Description string            `json:"description,omitempty"`
	Traits      []string          `json:"traits,omitempty"`
	Relations   *SocietyRelations `json:"relations,omitempty"`
}

// SocietyRelations 群体关系
type SocietyRelations struct {
	Allies  []string `json:"allies,omitempty"`
	Enemies []string `json:"enemies,omitempty"`
	Neutral []string `json:"neutral,omitempty"`
}

// Language 语言
type Language struct {
	Name     string   `json:"name"`
	Speakers []string `json:"speakers"`
	Script   string   `json:"script,omitempty"`
}

// Tradition 传统
type Tradition struct {
	Name         string   `json:"name"`
	Description  string   `json:"description"`
	Participants []string `json:"participants,omitempty"`
	Frequency    string   `json:"frequency,omitempty"`
}

// HistoryConfig 历史配置
type HistoryConfig struct {
	Eras    []Era    `json:"eras,omitempty"`
	Legends []Legend `json:"legends,omitempty"`
}

// Era 历史时代
type Era struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Duration    string   `json:"duration,omitempty"`
	KeyEvents   []string `json:"keyEvents,omitempty"`
}

// Legend 传说
type Legend struct {
	Title      string   `json:"title"`
	Summary    string   `json:"summary"`
	Characters []string `json:"characters,omitempty"`
	TruthLevel string   `json:"truthLevel,omitempty"`
}

// GeographyConfig 地理配置
type GeographyConfig struct {
	WorldType   string       `json:"worldType"`
	Size        string       `json:"size"`
	Regions     []Region     `json:"regions,omitempty"`
	Connections []Connection `json:"connections,omitempty"`
}

// Region 地理区域
type Region struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description,omitempty"`
	Climate     string `json:"climate,omitempty"`
	Population  string `json:"population,omitempty"`
	Governance  string `json:"governance,omitempty"`
}

// Connection 区域连接
type Connection struct {
	From        string `json:"from"`
	To          string `json:"to"`
	Method      string `json:"method"`
	Difficulty  string `json:"difficulty,omitempty"`
	Description string `json:"description,omitempty"`
}

// GenerateWorldsResponse AI生成世界配置响应
type GenerateWorldsResponse struct {
	Candidates []WorldCandidate `json:"candidates"`
}

// WorldCandidate 候选世界配置
type WorldCandidate struct {
	ID            string             `json:"id"`
	Name          string             `json:"name"`
	Configuration WorldConfiguration `json:"configuration"`
	Preview       WorldPreview       `json:"preview"`
}

// WorldPreview 世界预览信息
type WorldPreview struct {
	ShortDescription string   `json:"shortDescription"`
	MainFeatures     []string `json:"mainFeatures"`
	Theme            string   `json:"theme"`
	Difficulty       string   `json:"difficulty"`
}

// CreateCharacterRequest 创建角色请求
type CreateCharacterRequest struct {
	WorldID       uuid.UUID `json:"world_id" binding:"required"`       // 世界ID，必填，指定角色所属的游戏世界
	Name          string    `json:"name" binding:"required"`           // 角色名称，必填，角色的显示名称
	Description   string    `json:"description"`                       // 角色描述，可选，详细描述角色的外观、性格等
	CharacterType string    `json:"character_type" binding:"required"` // 角色类型，必填，如"玩家"、"NPC"、"商人"等
	Traits        []string  `json:"traits"`                            // 角色特质，可选，角色的性格特征和能力标签列表
}

// UpdateCharacterRequest 更新角色请求
type UpdateCharacterRequest struct {
	Name          string   `json:"name"`           // 角色名称，可选，更新角色的显示名称
	Description   string   `json:"description"`    // 角色描述，可选，更新角色的详细描述
	Traits        []string `json:"traits"`         // 角色特质，可选，更新角色的特征标签列表
	CharacterType string   `json:"character_type"` // 角色类型，可选，更新角色的类型分类
}

// AddTraitRequest 添加特质请求
type AddTraitRequest struct {
	Trait string `json:"trait" binding:"required"` // 特质名称，必填，要添加给角色的特质或能力标签
}

// AddMemoryRequest 添加记忆请求
type AddMemoryRequest struct {
	Type       string                 `json:"type" binding:"required"`           // 记忆类型，必填，如"事件"、"人物"、"地点"等
	Content    string                 `json:"content" binding:"required"`        // 记忆内容，必填，记忆的具体描述
	Importance int                    `json:"importance" binding:"min=1,max=10"` // 重要程度，必填，1-10级，影响记忆的保留和回忆
	Clarity    float64                `json:"clarity" binding:"min=0,max=1"`     // 清晰度，必填，0.0-1.0，表示记忆的清晰程度
	Tags       []string               `json:"tags"`                              // 标签，可选，用于分类和检索记忆的关键词
	RelatedIDs []uuid.UUID            `json:"related_ids"`                       // 关联ID，可选，与此记忆相关的其他实体ID列表
	Metadata   map[string]interface{} `json:"metadata"`                          // 元数据，可选，记忆的额外属性和信息
}

// AddExperienceRequest 添加阅历请求
type AddExperienceRequest struct {
	Type     string `json:"type" binding:"required"`
	Category string `json:"category" binding:"required"`
}

// PerformActionRequest 执行行动请求
type PerformActionRequest struct {
	WorldID    uuid.UUID              `json:"world_id" binding:"required"`    // 世界ID，必填，指定行动发生的游戏世界
	ActionType string                 `json:"action_type" binding:"required"` // 行动类型，必填，如"移动"、"攻击"、"交谈"等
	TargetType string                 `json:"target_type"`                    // 目标类型，可选，行动目标的类型，如"角色"、"物品"、"场景"
	TargetID   string                 `json:"target_id"`                      // 目标ID，可选，具体的行动目标标识符
	Parameters map[string]interface{} `json:"parameters"`                     // 行动参数，可选，行动所需的额外参数和配置
}

// InteractionRequest 交互请求
type InteractionRequest struct {
	WorldID         uuid.UUID              `json:"world_id" binding:"required"`
	InteractionType string                 `json:"interaction_type" binding:"required"`
	Content         string                 `json:"content"`
	Parameters      map[string]interface{} `json:"parameters"`
}

// SpeakRequest 说话请求
type SpeakRequest struct {
	WorldID           uuid.UUID  `json:"world_id" binding:"required"`    // 世界ID，必填，指定说话发生的游戏世界
	Content           string     `json:"content" binding:"required"`     // 说话内容，必填，角色要说的具体文字
	SpeechType        string     `json:"speech_type"`                    // 说话类型，可选，如"say"(普通说话)、"whisper"(耳语)、"shout"(大喊)、"think"(心理活动)
	TargetCharacterID *uuid.UUID `json:"target_character_id"`            // 目标角色ID，可选，私聊或定向说话的目标角色
	Volume            string     `json:"volume"`                         // 音量，可选，如"quiet"(安静)、"normal"(正常)、"loud"(大声)
	Emotion           string     `json:"emotion"`                        // 情感，可选，说话时的情绪状态，如"happy"(高兴)、"sad"(悲伤)、"angry"(愤怒)、"neutral"(中性)
}

// TriggerEventRequest 触发事件请求
type TriggerEventRequest struct {
	WorldID            uuid.UUID              `json:"world_id" binding:"required"`
	EventType          string                 `json:"event_type" binding:"required"`
	Name               string                 `json:"name" binding:"required"`
	Description        string                 `json:"description"`
	Priority           int                    `json:"priority"`
	Participants       []uuid.UUID            `json:"participants"`
	EventData          map[string]interface{} `json:"event_data"`
	ProcessImmediately bool                   `json:"process_immediately"`
}

// ActionResult 行动结果
type ActionResult struct {
	EventID     string                 `json:"event_id"`
	ActionType  string                 `json:"action_type"`
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	Effects     map[string]interface{} `json:"effects"`
	Timestamp   time.Time              `json:"timestamp"`
}

// InteractionResult 交互结果
type InteractionResult struct {
	EventID         string                 `json:"event_id"`
	InteractionType string                 `json:"interaction_type"`
	Success         bool                   `json:"success"`
	Message         string                 `json:"message"`
	Response        string                 `json:"response"`
	Effects         map[string]interface{} `json:"effects"`
	Timestamp       time.Time              `json:"timestamp"`
}

// SpeechResult 说话结果
type SpeechResult struct {
	EventID     string        `json:"event_id"`
	Content     string        `json:"content"`
	SpeechType  string        `json:"speech_type"`
	Volume      string        `json:"volume"`
	Emotion     string        `json:"emotion"`
	Listeners   []string      `json:"listeners"`
	Success     bool          `json:"success"`
	Message     string        `json:"message"`
	Timestamp   time.Time     `json:"timestamp"`
}

// EventResult 事件结果
type EventResult struct {
	EventID     string                 `json:"event_id"`
	EventType   string                 `json:"event_type"`
	Name        string                 `json:"name"`
	Status      string                 `json:"status"`
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	Effects     map[string]interface{} `json:"effects"`
	Timestamp   time.Time              `json:"timestamp"`
}

// UpdateTimeRequest 更新时间请求
type UpdateTimeRequest struct {
	Minutes int64 `json:"minutes" binding:"required,min=1"`
}

// UpdateCharacter 更新角色信息
// @Summary 更新角色信息
// @Description 更新指定角色的基本信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body UpdateCharacterRequest true "更新角色请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id} [put]
func (h *GameHandler) UpdateCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req UpdateCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	userIDStr := userID.String()
	err = h.characterService.UpdateCharacter(c.Request.Context(), characterID.String(), req.Name, req.Description, req.Traits, req.CharacterType, &userIDStr)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "更新角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新角色成功",
	})
}

// DeleteCharacter 删除角色
// @Summary 删除角色
// @Description 删除指定的角色
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id} [delete]
func (h *GameHandler) DeleteCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	userIDStr := userID.String()
	err = h.characterService.DeleteCharacter(c.Request.Context(), characterID.String(), &userIDStr)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "删除角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除角色成功",
	})
}

// AddCharacterTrait 添加角色特质
// @Summary 添加角色特质
// @Description 为指定角色添加新的特质
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body AddTraitRequest true "添加特质请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/traits [post]
func (h *GameHandler) AddCharacterTrait(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req AddTraitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	userIDStr := userID.String()
	err = h.characterService.AddCharacterTrait(c.Request.Context(), characterID.String(), req.Trait, &userIDStr)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "添加特质失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "添加特质成功",
	})
}

// AddCharacterMemory 添加角色记忆
// @Summary 添加角色记忆
// @Description 为指定角色添加新的记忆
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body AddMemoryRequest true "添加记忆请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/memories [post]
func (h *GameHandler) AddCharacterMemory(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req AddMemoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 转换RelatedIDs从[]uuid.UUID到[]string
	relatedIDStrs := make([]string, len(req.RelatedIDs))
	for i, id := range req.RelatedIDs {
		relatedIDStrs[i] = id.String()
	}

	memory := models.Memory{
		Type:       req.Type,
		Content:    req.Content,
		Importance: req.Importance,
		Clarity:    req.Clarity,
		Tags:       req.Tags,
		RelatedIDs: relatedIDStrs,
		Metadata:   req.Metadata,
	}

	userIDStr := userID.String()
	err = h.characterService.AddCharacterMemory(c.Request.Context(), characterID.String(), memory, &userIDStr)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "添加记忆失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "添加记忆成功",
	})
}

// AddCharacterExperience 添加角色阅历
// @Summary 添加角色阅历
// @Description 为指定角色添加新的阅历
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body AddExperienceRequest true "添加阅历请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/experiences [post]
func (h *GameHandler) AddCharacterExperience(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req AddExperienceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	userIDStr := userID.String()
	err = h.characterService.AddCharacterExperience(c.Request.Context(), characterID.String(), req.Type, req.Category, &userIDStr)
	if err != nil {
		if err.Error() == "角色不存在或无权限操作" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "角色不存在或无权限操作",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "添加阅历失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "添加阅历成功",
	})
}

// MoveCharacter 移动角色
// @Summary 移动角色
// @Description 移动角色到指定场景
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body MoveCharacterRequest true "移动角色请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/move [post]
func (h *GameHandler) MoveCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req MoveCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	userIDStr := userID.String()
	if err := h.characterService.MoveCharacter(c.Request.Context(), characterID.String(), req.SceneID.String(), &userIDStr); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "移动角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "角色移动成功",
	})
}

// CreateScene 创建场景
// @Summary 创建场景
// @Description 在指定世界中创建新场景
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateSceneRequest true "创建场景请求"
// @Success 200 {object} Response{data=models.Scene}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/scenes [post]
func (h *GameHandler) CreateScene(c *gin.Context) {
	var req CreateSceneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置默认属性
	if req.Properties == nil {
		req.Properties = make(map[string]interface{})
	}

	scene, err := h.sceneService.CreateScene(
		c.Request.Context(),
		req.WorldID.String(),
		req.Name,
		req.Description,
		req.SceneType,
		req.Properties,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建场景失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建场景成功",
		Data:    scene,
	})
}

// GetScene 获取场景信息
// @Summary 获取场景信息
// @Description 获取指定场景的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param scene_id path string true "场景ID"
// @Success 200 {object} Response{data=models.Scene}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/scenes/{scene_id} [get]
func (h *GameHandler) GetScene(c *gin.Context) {
	sceneIDStr := c.Param("scene_id")
	sceneID, err := uuid.Parse(sceneIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的场景ID格式",
		})
		return
	}

	scene, err := h.sceneService.GetScene(c.Request.Context(), sceneID.String())
	if err != nil {
		if err.Error() == "场景不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "场景不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取场景信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取场景信息成功",
		Data:    scene,
	})
}

// CreateEvent 创建事件
// @Summary 创建事件
// @Description 在指定世界中创建新事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateEventRequest true "创建事件请求"
// @Success 200 {object} Response{data=models.Event}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events [post]
func (h *GameHandler) CreateEvent(c *gin.Context) {
	var req CreateEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Priority == 0 {
		req.Priority = 5
	}
	if req.EventData == nil {
		req.EventData = make(map[string]interface{})
	}

	// 转换Participants从[]uuid.UUID到[]string
	participantStrs := make([]string, len(req.Participants))
	for i, id := range req.Participants {
		participantStrs[i] = id.String()
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID.String(),
		req.EventType,
		req.Name,
		req.Description,
		req.Priority,
		participantStrs,
		req.EventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建事件成功",
		Data:    event,
	})
}

// ProcessEvent 处理事件
// @Summary 处理事件
// @Description 处理指定的事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param event_id path string true "事件ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events/{event_id}/process [post]
func (h *GameHandler) ProcessEvent(c *gin.Context) {
	eventIDStr := c.Param("event_id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的事件ID格式",
		})
		return
	}

	if err := h.eventService.ProcessEvent(c.Request.Context(), eventID.String()); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "处理事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "事件处理成功",
	})
}

// PerformAction 执行角色行动
// @Summary 执行角色行动
// @Description 角色执行指定的行动，如移动、交互、说话等
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body PerformActionRequest true "行动请求"
// @Success 200 {object} Response{data=ActionResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/actions [post]
func (h *GameHandler) PerformAction(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req PerformActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 创建行动事件
	eventData := map[string]interface{}{
		"action_type":   req.ActionType,
		"character_id":  characterID.String(),
		"target_type":   req.TargetType,
		"target_id":     req.TargetID,
		"parameters":    req.Parameters,
		"user_id":       userID.String(),
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID.String(),
		"character_action",
		fmt.Sprintf("角色行动: %s", req.ActionType),
		fmt.Sprintf("角色 %s 执行 %s 行动", characterID.String(), req.ActionType),
		5, // 中等优先级
		[]string{characterID.String()},
		eventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建行动事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 立即处理事件
	if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "执行行动失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建行动结果
	result := ActionResult{
		EventID:     event.ID,
		ActionType:  req.ActionType,
		Success:     true,
		Message:     fmt.Sprintf("成功执行%s行动", req.ActionType),
		Effects:     map[string]interface{}{},
		Timestamp:   event.CreatedAt,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "行动执行成功",
		Data:    result,
	})
}

// InteractWithCharacter 与角色交互
// @Summary 与角色交互
// @Description 角色与另一个角色进行交互
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param target_character_id path string true "目标角色ID"
// @Param request body InteractionRequest true "交互请求"
// @Success 200 {object} Response{data=InteractionResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/interact/{target_character_id} [post]
func (h *GameHandler) InteractWithCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	targetCharacterIDStr := c.Param("target_character_id")
	targetCharacterID, err := uuid.Parse(targetCharacterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的目标角色ID格式",
		})
		return
	}

	var req InteractionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 创建交互事件
	eventData := map[string]interface{}{
		"action_type":       "interact",
		"character_id":      characterID.String(),
		"target_type":       "character",
		"target_id":         targetCharacterID.String(),
		"interaction_type":  req.InteractionType,
		"content":           req.Content,
		"parameters":        req.Parameters,
		"user_id":           userID.String(),
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID.String(),
		"character_action",
		fmt.Sprintf("角色交互: %s", req.InteractionType),
		fmt.Sprintf("角色 %s 与 %s 进行 %s 交互", characterID.String(), targetCharacterID.String(), req.InteractionType),
		5,
		[]string{characterID.String(), targetCharacterID.String()},
		eventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建交互事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 处理交互事件
	if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "执行交互失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建交互结果
	result := InteractionResult{
		EventID:         event.ID,
		InteractionType: req.InteractionType,
		Success:         true,
		Message:         fmt.Sprintf("成功与角色进行%s交互", req.InteractionType),
		Response:        "", // 这里可以根据交互类型生成响应
		Effects:         map[string]interface{}{},
		Timestamp:       event.CreatedAt,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "交互执行成功",
		Data:    result,
	})
}

// SpeakInScene 在场景中说话
// @Summary 在场景中说话
// @Description 角色在当前场景中说话，其他角色可以听到
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body SpeakRequest true "说话请求"
// @Success 200 {object} Response{data=SpeechResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/speak [post]
func (h *GameHandler) SpeakInScene(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req SpeakRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 创建说话事件
	eventData := map[string]interface{}{
		"action_type": "speak",
		"character_id": characterID.String(),
		"content": req.Content,
		"speech_type": req.SpeechType,
		"target_character_id": req.TargetCharacterID,
		"volume": req.Volume,
		"emotion": req.Emotion,
		"user_id": userID.String(),
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID.String(),
		"character_action",
		"角色说话",
		fmt.Sprintf("角色 %s 说话: %s", characterID.String(), req.Content),
		3, // 较低优先级
		[]string{characterID.String()},
		eventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建说话事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 处理说话事件
	if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "执行说话失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建说话结果
	result := SpeechResult{
		EventID:     event.ID,
		Content:     req.Content,
		SpeechType:  req.SpeechType,
		Volume:      req.Volume,
		Emotion:     req.Emotion,
		Listeners:   []string{}, // 这里可以查询当前场景中的其他角色
		Success:     true,
		Message:     "说话成功",
		Timestamp:   event.CreatedAt,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "说话成功",
		Data:    result,
	})
}

// TriggerEvent 触发事件
// @Summary 触发事件
// @Description 手动触发一个游戏事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body TriggerEventRequest true "触发事件请求"
// @Success 200 {object} Response{data=EventResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events/trigger [post]
func (h *GameHandler) TriggerEvent(c *gin.Context) {
	var req TriggerEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 添加用户ID到事件数据
	if req.EventData == nil {
		req.EventData = make(map[string]interface{})
	}
	req.EventData["triggered_by_user"] = userID.String()

	// 转换Participants从[]uuid.UUID到[]string
	participantStrs := make([]string, len(req.Participants))
	for i, id := range req.Participants {
		participantStrs[i] = id.String()
	}

	// 创建事件
	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID.String(),
		req.EventType,
		req.Name,
		req.Description,
		req.Priority,
		participantStrs,
		req.EventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建事件失败",
			Error:   err.Error(),
		})
		return
	}

	// 如果需要立即处理事件
	if req.ProcessImmediately {
		if err := h.eventService.ProcessEvent(c.Request.Context(), event.ID); err != nil {
			c.JSON(http.StatusInternalServerError, Response{
				Success: false,
				Message: "处理事件失败",
				Error:   err.Error(),
			})
			return
		}
	}

	// 构建事件结果
	result := EventResult{
		EventID:     event.ID,
		EventType:   req.EventType,
		Name:        req.Name,
		Status:      event.Status,
		Success:     true,
		Message:     "事件创建成功",
		Effects:     map[string]interface{}{},
		Timestamp:   event.CreatedAt,
	}

	if req.ProcessImmediately {
		result.Message = "事件创建并处理成功"
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: result.Message,
		Data:    result,
	})
}

// GetWorldState 获取世界状态
// @Summary 获取世界状态
// @Description 获取指定世界的当前状态信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response{data=game.GameState}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/state [get]
func (h *GameHandler) GetWorldState(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	state, err := h.stateService.GetWorldState(c.Request.Context(), worldID.String())
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界状态失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界状态成功",
		Data:    state,
	})
}

// UpdateWorldTime 更新世界时间
// @Summary 更新世界时间
// @Description 推进世界的游戏时间
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Param request body UpdateTimeRequest true "时间更新请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/time [post]
func (h *GameHandler) UpdateWorldTime(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	var req UpdateTimeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.stateService.UpdateWorldTime(c.Request.Context(), worldID.String(), req.Minutes); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "更新世界时间失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: fmt.Sprintf("成功推进世界时间 %d 分钟", req.Minutes),
	})
}

// ProcessWorldTick 处理世界时钟周期
// @Summary 处理世界时钟周期
// @Description 执行世界的一个时钟周期，包括时间推进、NPC行为、环境事件等
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/tick [post]
func (h *GameHandler) ProcessWorldTick(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	_, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.stateService.ProcessWorldTick(c.Request.Context(), worldID.String()); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "处理世界时钟周期失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "世界时钟周期处理成功",
	})
}

// 请求结构体定义

// MoveCharacterRequest 移动角色请求
type MoveCharacterRequest struct {
	SceneID uuid.UUID `json:"scene_id" binding:"required"`
}

// CreateSceneRequest 创建场景请求
type CreateSceneRequest struct {
	WorldID     uuid.UUID              `json:"world_id" binding:"required"`
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	SceneType   string                 `json:"scene_type" binding:"required"`
	Properties  map[string]interface{} `json:"properties"`
}

// CreateEventRequest 创建事件请求
type CreateEventRequest struct {
	WorldID      uuid.UUID              `json:"world_id" binding:"required"`
	EventType    string                 `json:"event_type" binding:"required"`
	Name         string                 `json:"name" binding:"required"`
	Description  string                 `json:"description"`
	Priority     int                    `json:"priority"`
	Participants []uuid.UUID            `json:"participants"`
	EventData    map[string]interface{} `json:"event_data"`
}

// UpdateWorld 更新世界信息
// @Summary 更新世界信息
// @Description 更新指定世界的信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Param request body UpdateWorldRequest true "更新世界请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id} [put]
func (h *GameHandler) UpdateWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	var req UpdateWorldRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 构建更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != nil {
		updates["description"] = req.Description
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.MaxPlayers != nil {
		updates["max_players"] = *req.MaxPlayers
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	// 验证用户权限（只有创建者可以更新）
	world, err := h.worldService.GetWorld(c.Request.Context(), worldID.String())
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界信息失败",
			Error:   err.Error(),
		})
		return
	}

	if world.CreatorID != userID.String() {
		c.JSON(http.StatusForbidden, Response{
			Success: false,
			Message: "无权限修改此世界",
		})
		return
	}

	if err := h.worldService.UpdateWorld(c.Request.Context(), worldID.String(), updates); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "更新世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新世界成功",
	})
}

// DeleteWorld 删除世界
// @Summary 删除世界
// @Description 删除指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id} [delete]
func (h *GameHandler) DeleteWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.DeleteWorld(c.Request.Context(), worldID.String(), userID.String()); err != nil {
		if err.Error() == "世界不存在或无权限删除" {
			c.JSON(http.StatusForbidden, Response{
				Success: false,
				Message: "世界不存在或无权限删除",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "删除世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除世界成功",
	})
}

// UpdateWorldRequest 更新世界请求
type UpdateWorldRequest struct {
	Name        string  `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	IsPublic    *bool   `json:"is_public,omitempty"`
	MaxPlayers  *int    `json:"max_players,omitempty"`
	Status      string  `json:"status,omitempty"`
}

// PaginatedResponse 分页响应
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int64       `json:"total_pages"`
}

// generateInitialWorldContent 异步生成世界的初始内容
func (h *GameHandler) generateInitialWorldContent(world *models.World, req CreateWorldRequest) {
	ctx := context.Background()

	h.logger.Info("开始为新世界生成初始内容",
		"world_id", world.ID,
		"world_name", world.Name,
		"theme", req.Theme)

	// 生成主场景
	if err := h.generateMainScene(ctx, world, req); err != nil {
		h.logger.Error("生成主场景失败", "error", err, "world_id", world.ID)
	}

	// 生成初始NPC
	if err := h.generateInitialNPCs(ctx, world, req); err != nil {
		h.logger.Error("生成初始NPC失败", "error", err, "world_id", world.ID)
	}

	h.logger.Info("世界初始内容生成完成", "world_id", world.ID)
}

// generateMainScene 生成世界的主场景
func (h *GameHandler) generateMainScene(ctx context.Context, world *models.World, req CreateWorldRequest) error {
	// 构建场景生成请求
	scenePrompt := fmt.Sprintf(
		"为名为'%s'的%s类型世界生成主要场景。世界描述：%s。请生成一个作为游戏起始点的主场景，包含详细的环境描述、氛围设定和可能的行动选项。",
		world.Name, req.Theme, *world.Description)

	aiReq := &ai.GenerateRequest{
		Type:        "scene",
		Prompt:      scenePrompt,
		Context: map[string]interface{}{
			"world_name": world.Name,
			"theme":      req.Theme,
			"scene_type": "main",
		},
		MaxTokens:   600,
		Temperature: 0.7,
		WorldID:     &world.ID,
		Schema: map[string]interface{}{
			"name":             "string",
			"description":      "string",
			"atmosphere":       "string",
			"key_features":     "array",
			"possible_actions": "array",
		},
	}

	response, err := h.aiService.GenerateContent(ctx, aiReq)
	if err != nil {
		return fmt.Errorf("AI生成场景失败: %w", err)
	}

	// 创建场景记录
	sceneName := "主场景"
	sceneDescription := response.Content

	if response.StructuredData != nil {
		if name, ok := response.StructuredData["name"].(string); ok && name != "" {
			sceneName = name
		}
		if desc, ok := response.StructuredData["description"].(string); ok && desc != "" {
			sceneDescription = desc
		}
	}

	// 构建场景属性
	sceneProperties := make(map[string]interface{})
	if response.StructuredData != nil {
		sceneProperties = response.StructuredData
	}

	scene, err := h.sceneService.CreateScene(
		ctx,
		world.ID,
		sceneName,
		sceneDescription,
		"main",
		sceneProperties,
	)
	if err != nil {
		return fmt.Errorf("创建场景记录失败: %w", err)
	}

	h.logger.Info("成功生成主场景",
		"world_id", world.ID,
		"scene_id", scene.ID,
		"scene_name", sceneName)

	return nil
}

// generateInitialNPCs 生成世界的初始NPC
func (h *GameHandler) generateInitialNPCs(ctx context.Context, world *models.World, req CreateWorldRequest) error {
	// 生成2-3个初始NPC
	npcCount := 2
	if req.MaxPlayers > 5 {
		npcCount = 3
	}

	for i := 0; i < npcCount; i++ {
		if err := h.generateSingleNPC(ctx, world, req, i+1); err != nil {
			h.logger.Error("生成NPC失败", "error", err, "world_id", world.ID, "npc_index", i+1)
			// 继续生成其他NPC，不因为一个失败而停止
		}
	}

	return nil
}

// generateSingleNPC 生成单个NPC
func (h *GameHandler) generateSingleNPC(ctx context.Context, world *models.World, req CreateWorldRequest, index int) error {
	// 构建NPC生成请求
	npcPrompt := fmt.Sprintf(
		"为名为'%s'的%s类型世界生成第%d个NPC角色。世界描述：%s。请生成一个有趣且符合世界设定的NPC，包含姓名、外观、性格、背景故事和对话风格。",
		world.Name, req.Theme, index, *world.Description)

	aiReq := &ai.GenerateRequest{
		Type:        "character",
		Prompt:      npcPrompt,
		Context: map[string]interface{}{
			"world_name":     world.Name,
			"theme":          req.Theme,
			"character_type": "npc",
			"index":          index,
		},
		MaxTokens:   500,
		Temperature: 0.8,
		WorldID:     &world.ID,
		Schema: map[string]interface{}{
			"name":           "string",
			"description":    "string",
			"personality":    "array",
			"background":     "string",
			"dialogue_style": "string",
		},
	}

	response, err := h.aiService.GenerateContent(ctx, aiReq)
	if err != nil {
		return fmt.Errorf("AI生成NPC失败: %w", err)
	}

	// 创建NPC角色记录
	npcName := fmt.Sprintf("NPC-%d", index)
	npcDescription := response.Content
	var traits []string

	if response.StructuredData != nil {
		if name, ok := response.StructuredData["name"].(string); ok && name != "" {
			npcName = name
		}
		if desc, ok := response.StructuredData["description"].(string); ok && desc != "" {
			npcDescription = desc
		}
		if personality, ok := response.StructuredData["personality"].([]interface{}); ok {
			for _, trait := range personality {
				if traitStr, ok := trait.(string); ok {
					traits = append(traits, traitStr)
				}
			}
		}
	}

	character, err := h.characterService.CreateCharacter(
		ctx,
		world.ID,
		nil, // NPC没有用户ID
		npcName,
		npcDescription,
		"npc",
		traits,
	)
	if err != nil {
		return fmt.Errorf("创建NPC记录失败: %w", err)
	}

	h.logger.Info("成功生成NPC",
		"world_id", world.ID,
		"character_id", character.ID,
		"character_name", npcName)

	return nil
}

// generateWorldCandidates 生成多个候选世界配置
func (h *GameHandler) generateWorldCandidates(ctx context.Context, req GenerateWorldsRequest, userID string) ([]WorldCandidate, error) {
	// 生成3-5个候选世界配置
	candidateCount := 4
	candidates := make([]WorldCandidate, 0, candidateCount)

	// 构建AI生成请求的基础提示词
	basePrompt := h.buildWorldGenerationPrompt(req)

	for i := 0; i < candidateCount; i++ {
		// 为每个候选世界添加变化因子
		prompt := h.addVariationToPrompt(basePrompt, i)

		// 调用AI服务生成世界配置
		aiReq := &ai.GenerateRequest{
			Type:        "world",
			Prompt:      prompt,
			Context:     req.WorldSettings,
			MaxTokens:   1500,
			Temperature: 0.8, // 较高的温度以增加多样性
			UserID:      &userID,
			Schema: map[string]interface{}{
				"worldDescription": "string",
				"worldRules":       "array",
				"environment":      "object",
				"culture":          "object",
				"history":          "object",
				"geography":        "object",
			},
		}

		h.logger.Info("开始生成候选世界配置", "candidate_index", i+1, "prompt_length", len(prompt))

		response, err := h.aiService.GenerateContent(ctx, aiReq)
		if err != nil {
			h.logger.Error("生成候选世界配置失败", "error", err, "candidate_index", i+1)
			// 继续生成其他候选配置，不因为一个失败而停止
			continue
		}

		// 解析AI响应为世界配置
		candidate, err := h.parseWorldConfiguration(response, req.WorldName, i+1)
		if err != nil {
			h.logger.Error("解析世界配置失败", "error", err, "candidate_index", i+1)
			continue
		}

		candidates = append(candidates, candidate)
		h.logger.Info("成功生成候选世界配置", "candidate_index", i+1, "candidate_id", candidate.ID)
	}

	// 确保至少有一个候选配置
	if len(candidates) == 0 {
		return nil, fmt.Errorf("无法生成任何有效的世界配置")
	}

	return candidates, nil
}

// buildWorldGenerationPrompt 构建世界生成的基础提示词
func (h *GameHandler) buildWorldGenerationPrompt(req GenerateWorldsRequest) string {
	prompt := fmt.Sprintf(`请为名为"%s"的游戏世界生成详细的配置信息。

要求：
1. 生成一个引人入胜的世界描述（50-500字）
2. 设计3-8条世界规则，涵盖不同类别（魔法、战斗、社交、经济等）
3. 详细描述环境设定，包括气候、地形和资源
4. 设计丰富的文化背景，包括种族、语言和传统
5. 创造有趣的历史背景和传说故事
6. 规划合理的地理结构和区域分布

世界名称：%s`, req.WorldName, req.WorldName)

	// 添加用户设置的偏好
	if req.WorldSettings != nil {
		if theme, ok := req.WorldSettings["theme"].(string); ok && theme != "" {
			prompt += fmt.Sprintf("\n主题风格：%s", theme)
		}
		if style, ok := req.WorldSettings["style"].(string); ok && style != "" {
			prompt += fmt.Sprintf("\n世界风格：%s", style)
		}
		if difficulty, ok := req.WorldSettings["difficulty"].(string); ok && difficulty != "" {
			prompt += fmt.Sprintf("\n难度设定：%s", difficulty)
		}
		if setting, ok := req.WorldSettings["setting"].(string); ok && setting != "" {
			prompt += fmt.Sprintf("\n背景设定：%s", setting)
		}
	}

	prompt += `

请确保生成的内容：
- 具有内在一致性和逻辑性
- 富有创意和想象力
- 适合文本角色扮演游戏
- 包含足够的细节以支持游戏玩法
- 遵循JSON Schema格式要求

请以JSON格式返回完整的世界配置。`

	return prompt
}

// addVariationToPrompt 为提示词添加变化因子以生成不同的候选配置
func (h *GameHandler) addVariationToPrompt(basePrompt string, index int) string {
	variations := []string{
		"注重魔法和神秘元素，创造一个充满奇幻色彩的世界。",
		"强调政治斗争和权力游戏，设计复杂的社会关系。",
		"突出冒险和探索元素，创造广阔的未知领域。",
		"重视文化多样性和种族融合，展现丰富的社会结构。",
		"聚焦于古老传说和历史遗迹，营造深厚的历史底蕴。",
	}

	if index < len(variations) {
		return basePrompt + "\n\n特别要求：" + variations[index]
	}

	return basePrompt
}

// parseWorldConfiguration 解析AI响应为世界配置
func (h *GameHandler) parseWorldConfiguration(response *ai.GenerateResponse, worldName string, index int) (WorldCandidate, error) {
	// 生成候选配置ID
	candidateID := fmt.Sprintf("candidate_%d_%d", time.Now().Unix(), index)

	// 尝试解析结构化数据
	var config WorldConfiguration
	if response.StructuredData != nil {
		// 将结构化数据转换为WorldConfiguration
		configBytes, err := json.Marshal(response.StructuredData)
		if err != nil {
			h.logger.Error("序列化结构化数据失败", "error", err)
		} else {
			if err := json.Unmarshal(configBytes, &config); err != nil {
				h.logger.Error("解析结构化数据失败", "error", err)
			}
		}
	}

	// 如果结构化数据解析失败，尝试从内容中提取
	if config.WorldDescription == "" && response.Content != "" {
		// 尝试从内容中解析JSON
		var contentConfig WorldConfiguration
		if err := json.Unmarshal([]byte(response.Content), &contentConfig); err == nil {
			config = contentConfig
		} else {
			// 如果无法解析JSON，使用内容作为描述
			config.WorldDescription = response.Content
		}
	}

	// 确保有基本的世界描述
	if config.WorldDescription == "" {
		config.WorldDescription = fmt.Sprintf("一个名为%s的神秘世界，等待着勇敢的冒险者来探索其中的奥秘。", worldName)
	}

	// 生成预览信息
	preview := h.generateWorldPreview(config, worldName)

	candidate := WorldCandidate{
		ID:            candidateID,
		Name:          fmt.Sprintf("%s - 配置%d", worldName, index),
		Configuration: config,
		Preview:       preview,
	}

	return candidate, nil
}

// generateWorldPreview 生成世界预览信息
func (h *GameHandler) generateWorldPreview(config WorldConfiguration, worldName string) WorldPreview {
	// 生成简短描述（取世界描述的前100个字符）
	shortDesc := config.WorldDescription
	if len(shortDesc) > 100 {
		shortDesc = shortDesc[:100] + "..."
	}

	// 提取主要特征
	features := make([]string, 0, 5)

	// 从环境中提取特征
	if config.Environment != nil {
		if config.Environment.Climate != nil && config.Environment.Climate.Type != "" {
			features = append(features, fmt.Sprintf("%s气候", config.Environment.Climate.Type))
		}
		if config.Environment.Terrain != nil && config.Environment.Terrain.PrimaryTerrain != "" {
			features = append(features, fmt.Sprintf("%s地形", config.Environment.Terrain.PrimaryTerrain))
		}
	}

	// 从文化中提取特征
	if config.Culture != nil && len(config.Culture.Societies) > 0 {
		features = append(features, fmt.Sprintf("%d个种族", len(config.Culture.Societies)))
	}

	// 从历史中提取特征
	if config.History != nil && len(config.History.Eras) > 0 {
		features = append(features, fmt.Sprintf("%d个历史时代", len(config.History.Eras)))
	}

	// 从地理中提取特征
	if config.Geography != nil && len(config.Geography.Regions) > 0 {
		features = append(features, fmt.Sprintf("%d个地区", len(config.Geography.Regions)))
	}

	// 如果没有提取到特征，添加默认特征
	if len(features) == 0 {
		features = append(features, "丰富的世界设定", "独特的文化背景", "精彩的冒险体验")
	}

	// 确定主题
	theme := "fantasy" // 默认主题
	if config.Environment != nil && config.Environment.Terrain != nil {
		switch config.Environment.Terrain.PrimaryTerrain {
		case "desert":
			theme = "desert"
		case "mountains":
			theme = "mountain"
		case "forests":
			theme = "forest"
		case "islands":
			theme = "maritime"
		case "underground":
			theme = "underground"
		}
	}

	// 确定难度
	difficulty := "medium" // 默认难度
	if len(config.WorldRules) > 6 {
		difficulty = "hard"
	} else if len(config.WorldRules) < 3 {
		difficulty = "easy"
	}

	return WorldPreview{
		ShortDescription: shortDesc,
		MainFeatures:     features,
		Theme:            theme,
		Difficulty:       difficulty,
	}
}
