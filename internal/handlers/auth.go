package handlers

import (
	"net/http"
	"time"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *auth.Service
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService *auth.Service) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// GetProviders 获取支持的认证提供商
// @Summary 获取认证提供商列表
// @Description 获取系统支持的OAuth认证提供商列表
// @Tags 认证
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=[]string}
// @Router /auth/providers [get]
func (h *AuthHandler) GetProviders(c *gin.Context) {
	providers := h.authService.GetProviders()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取认证提供商列表成功",
		Data:    providers,
	})
}

// GetAuthURL 获取认证URL
// @Summary 获取OAuth认证URL
// @Description 获取指定提供商的OAuth认证URL
// @Tags 认证
// @Accept json
// @Produce json
// @Param provider path string true "认证提供商" Enums(google,github)
// @Param state query string false "状态参数"
// @Success 200 {object} Response{data=object{auth_url=string}}
// @Failure 400 {object} Response
// @Router /auth/{provider}/url [get]
func (h *AuthHandler) GetAuthURL(c *gin.Context) {
	provider := c.Param("provider")
	state := c.DefaultQuery("state", uuid.New().String())

	authURL, err := h.authService.GetAuthURL(provider, state)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "获取认证URL失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取认证URL成功",
		Data: gin.H{
			"auth_url": authURL,
			"state":    state,
		},
	})
}

// HandleCallback 处理OAuth回调
// @Summary 处理OAuth回调
// @Description 处理OAuth认证回调，完成用户登录
// @Tags 认证
// @Accept json
// @Produce json
// @Param provider path string true "认证提供商" Enums(google,github)
// @Param code query string true "授权码"
// @Param state query string false "状态参数"
// @Success 200 {object} Response{data=object{user=models.User,token=string,expires_at=string}}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /auth/{provider}/callback [get]
func (h *AuthHandler) HandleCallback(c *gin.Context) {
	provider := c.Param("provider")
	code := c.Query("code")
	state := c.Query("state")

	if code == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "缺少授权码",
		})
		return
	}

	user, token, err := h.authService.HandleCallback(provider, code, state)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "认证失败",
			Error:   err.Error(),
		})
		return
	}

	// 计算token过期时间
	claims, _ := h.authService.ValidateToken(token)
	expiresAt := ""
	if claims != nil {
		expiresAt = claims.ExpiresAt.Time.Format(time.RFC3339)
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "认证成功",
		Data: gin.H{
			"user":       user,
			"token":      token,
			"expires_at": expiresAt,
		},
	})
}

// RefreshToken 刷新token
// @Summary 刷新JWT token
// @Description 刷新即将过期的JWT token
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=object{token=string,expires_at=string}}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "缺少Authorization header",
		})
		return
	}

	// 提取token
	token := authHeader
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		token = authHeader[7:]
	}

	newToken, err := h.authService.RefreshToken(token)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "刷新token失败",
			Error:   err.Error(),
		})
		return
	}

	// 计算新token过期时间
	claims, _ := h.authService.ValidateToken(newToken)
	expiresAt := ""
	if claims != nil {
		expiresAt = claims.ExpiresAt.Time.Format(time.RFC3339)
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "刷新token成功",
		Data: gin.H{
			"token":      newToken,
			"expires_at": expiresAt,
		},
	})
}

// GetProfile 获取用户资料
// @Summary 获取当前用户资料
// @Description 获取当前认证用户的详细资料信息
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=models.User}
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Router /auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	user, err := h.authService.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "用户不存在",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取用户资料成功",
		Data:    user,
	})
}

// UpdateProfile 更新用户资料
// @Summary 更新用户资料
// @Description 更新当前用户的资料信息
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param profile body UpdateProfileRequest true "用户资料"
// @Success 200 {object} Response{data=models.User}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Router /auth/profile [put]
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户
	_, err := h.authService.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Message: "用户不存在",
			Error:   err.Error(),
		})
		return
	}

	// 更新用户信息
	updates := make(map[string]interface{})
	if req.DisplayName != nil {
		updates["display_name"] = *req.DisplayName
	}
	if req.Preferences != nil {
		updates["preferences"] = models.JSON(*req.Preferences)
	}

	if len(updates) > 0 {
		// 这里需要通过authService提供的方法来更新用户信息
		// 暂时跳过更新逻辑，在后续完善
		// TODO: 在authService中添加UpdateUser方法
	}

	// 重新获取更新后的用户信息
	updatedUser, err := h.authService.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取更新后的用户信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新用户资料成功",
		Data:    updatedUser,
	})
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出（客户端需要删除本地token）
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// JWT是无状态的，服务端无需处理登出逻辑
	// 客户端需要删除本地存储的token
	// 如果需要实现token黑名单，可以在这里添加逻辑

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "登出成功",
	})
}

// UpdateProfileRequest 更新用户资料请求
type UpdateProfileRequest struct {
	DisplayName *string                 `json:"display_name,omitempty"`
	Preferences *map[string]interface{} `json:"preferences,omitempty"`
}

// Response 统一响应结构
type Response struct {
	Success   bool        `json:"success"`             // 请求是否成功，true表示成功，false表示失败
	Message   string      `json:"message"`             // 响应消息，提供操作结果的描述信息
	Data      interface{} `json:"data,omitempty"`      // 响应数据，成功时包含具体的业务数据，失败时可为空
	Error     string      `json:"error,omitempty"`     // 错误信息，失败时包含详细的错误描述，成功时为空
	Timestamp int64       `json:"timestamp"`           // 响应时间戳，Unix时间戳格式，表示响应生成的时间
}

// NewResponse 创建响应
func NewResponse(success bool, message string, data interface{}, err string) Response {
	return Response{
		Success:   success,
		Message:   message,
		Data:      data,
		Error:     err,
		Timestamp: time.Now().Unix(),
	}
}
