package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AIHandler AI处理器
type AIHandler struct {
	aiService *ai.Service
	logger    logger.Logger
}

// NewAIHandler 创建AI处理器
func NewAIHandler(aiService *ai.Service) *AIHandler {
	return &AIHandler{
		aiService: aiService,
		logger:    logger.New("ai-handler"),
	}
}

// GenerateContent 生成内容
// @Summary 生成AI内容
// @Description 使用AI生成游戏内容，如场景、角色、事件等
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body ai.GenerateRequest true "生成请求"
// @Success 200 {object} Response{data=ai.GenerateResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /ai/generate [post]
func (h *AIHandler) GenerateContent(c *gin.Context) {
	var req ai.GenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	if userID, exists := auth.GetCurrentUserID(c); exists {
		userIDStr := userID.String()
		req.UserID = &userIDStr
	}

	// 验证必要参数
	if req.Type == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "生成类型不能为空",
		})
		return
	}

	if req.Prompt == "" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "提示词不能为空",
		})
		return
	}

	// 设置默认值
	if req.MaxTokens == 0 {
		req.MaxTokens = 500
	}
	if req.Temperature == 0 {
		req.Temperature = 0.7
	}

	// 生成内容
	response, err := h.aiService.GenerateContent(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成内容失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成内容成功",
		Data:    response,
	})
}

// GenerateScene 生成场景
// @Summary 生成游戏场景
// @Description 根据场景参数生成游戏场景描述和属性
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body GenerateSceneRequest true "场景生成请求"
// @Success 200 {object} Response{data=ai.GenerateResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /ai/generate/scene [post]
func (h *AIHandler) GenerateScene(c *gin.Context) {
	var req GenerateSceneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("场景生成请求参数绑定失败", "error", err)
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("收到场景生成请求",
		"world_id", req.WorldID,
		"scene_name", req.SceneName,
		"theme", req.Theme,
		"mood", req.Mood)

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 构建提示词
	prompt := h.buildScenePrompt(req)
	h.logger.Info("构建的场景生成提示词", "prompt", prompt)

	// 解析世界ID
	var worldID *uuid.UUID
	if req.WorldID != "" && req.WorldID != "temp" {
		if parsedID, err := uuid.Parse(req.WorldID); err == nil {
			worldID = &parsedID
		}
	}

	// 构建AI请求
	aiReq := &ai.GenerateRequest{
		Type:        "scene",
		Prompt:      prompt,
		Context:     h.buildSceneContext(req),
		MaxTokens:   500,
		Temperature: 0.7,
		WorldID:     func() *string { s := worldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"atmosphere":  "string",
			"connections": "object",
			"entities":    "array",
		},
	}

	h.logger.Info("开始调用AI服务生成场景", "ai_request_type", aiReq.Type)
	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		h.logger.Error("AI场景生成失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成场景失败",
			Error:   err.Error(),
		})
		return
	}

	h.logger.Info("AI场景生成成功", "token_usage", response.TokenUsage)
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成场景成功",
		Data:    response,
	})
}

// GenerateCharacter 生成角色
// @Summary 生成游戏角色
// @Description 根据提示词生成游戏角色描述和属性
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body GenerateCharacterRequest true "角色生成请求"
// @Success 200 {object} Response{data=ai.GenerateResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /ai/generate/character [post]
func (h *AIHandler) GenerateCharacter(c *gin.Context) {
	var req GenerateCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 构建AI请求
	aiReq := &ai.GenerateRequest{
		Type:        "character",
		Prompt:      req.Prompt,
		Context:     req.Context,
		MaxTokens:   400,
		Temperature: 0.8,
		WorldID:     func() *string { s := req.WorldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"personality": "array",
			"skills":      "array",
			"background":  "string",
		},
	}

	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成角色成功",
		Data:    response,
	})
}

// GenerateEvent 生成事件
// @Summary 生成游戏事件
// @Description 根据提示词生成游戏事件描述和效果
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body GenerateEventRequest true "事件生成请求"
// @Success 200 {object} Response{data=ai.GenerateResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /ai/generate/event [post]
func (h *AIHandler) GenerateEvent(c *gin.Context) {
	var req GenerateEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 构建AI请求
	aiReq := &ai.GenerateRequest{
		Type:        "event",
		Prompt:      req.Prompt,
		Context:     req.Context,
		MaxTokens:   300,
		Temperature: 0.6,
		WorldID:     func() *string { s := req.WorldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"priority":    "number",
			"duration":    "number",
			"effects":     "object",
		},
	}

	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "生成事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "生成事件成功",
		Data:    response,
	})
}

// GetInteractionHistory 获取AI交互历史
// @Summary 获取AI交互历史
// @Description 获取用户或世界的AI交互历史记录
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id query string false "世界ID"
// @Param limit query int false "限制数量" default(50)
// @Success 200 {object} Response{data=[]models.AIInteraction}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Router /ai/history [get]
func (h *AIHandler) GetInteractionHistory(c *gin.Context) {
	// 获取查询参数
	worldIDStr := c.Query("world_id")
	limitStr := c.DefaultQuery("limit", "50")

	var worldID *uuid.UUID
	if worldIDStr != "" {
		if id, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &id
		} else {
			c.JSON(http.StatusBadRequest, Response{
				Success: false,
				Message: "无效的世界ID格式",
			})
			return
		}
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200 // 限制最大数量
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 获取交互历史
	interactions, err := h.aiService.GetInteractionHistory(worldID, userID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取交互历史失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取交互历史成功",
		Data:    interactions,
	})
}

// GetTokenUsageStats 获取token使用统计
// @Summary 获取token使用统计
// @Description 获取用户或世界的token使用统计信息
// @Tags AI
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id query string false "世界ID"
// @Param days query int false "统计天数" default(30)
// @Success 200 {object} Response{data=object}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Router /ai/stats [get]
func (h *AIHandler) GetTokenUsageStats(c *gin.Context) {
	// 获取查询参数
	worldIDStr := c.Query("world_id")
	daysStr := c.DefaultQuery("days", "30")

	var worldID *uuid.UUID
	if worldIDStr != "" {
		if id, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &id
		} else {
			c.JSON(http.StatusBadRequest, Response{
				Success: false,
				Message: "无效的世界ID格式",
			})
			return
		}
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}
	if days > 365 {
		days = 365 // 限制最大天数
	}

	// 获取当前用户ID
	var userID *uuid.UUID
	if id, exists := auth.GetCurrentUserID(c); exists {
		userID = &id
	}

	// 获取统计信息
	stats, err := h.aiService.GetTokenUsageStats(worldID, userID, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取统计信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取统计信息成功",
		Data:    stats,
	})
}

// 请求结构体定义

// GenerateSceneRequest 场景生成请求
type GenerateSceneRequest struct {
	WorldID              string   `json:"world_id" binding:"required"`              // 世界ID
	SceneName            string   `json:"scene_name,omitempty"`                     // 场景名称
	SceneType            string   `json:"scene_type,omitempty"`                     // 场景类型
	Theme                string   `json:"theme,omitempty"`                          // 主题
	Mood                 string   `json:"mood,omitempty"`                           // 氛围
	ConnectedScenes      []string `json:"connected_scenes,omitempty"`               // 连接的场景
	SpecialRequirements  string   `json:"special_requirements,omitempty"`           // 特殊要求
	// 兼容旧格式
	Prompt               string                 `json:"prompt,omitempty"`         // 提示词（可选，用于兼容）
	Context              map[string]interface{} `json:"context,omitempty"`        // 上下文（可选，用于兼容）
}

// buildScenePrompt 构建场景生成提示词
func (h *AIHandler) buildScenePrompt(req GenerateSceneRequest) string {
	// 如果提供了旧格式的prompt，直接使用
	if req.Prompt != "" {
		return req.Prompt
	}

	// 构建新格式的提示词
	prompt := "请生成一个游戏场景的详细描述。"

	if req.SceneName != "" {
		prompt += fmt.Sprintf("场景名称：%s。", req.SceneName)
	}

	if req.Theme != "" {
		prompt += fmt.Sprintf("主题风格：%s。", req.Theme)
	}

	if req.SceneType != "" {
		prompt += fmt.Sprintf("场景类型：%s。", req.SceneType)
	}

	if req.Mood != "" {
		prompt += fmt.Sprintf("氛围设定：%s。", req.Mood)
	}

	if req.SpecialRequirements != "" {
		prompt += fmt.Sprintf("特殊要求：%s。", req.SpecialRequirements)
	}

	if len(req.ConnectedScenes) > 0 {
		prompt += fmt.Sprintf("需要连接的场景：%s。", strings.Join(req.ConnectedScenes, "、"))
	}

	prompt += "请生成包含场景名称、详细描述、氛围、关键特征和可能的行动的完整场景信息。"

	return prompt
}

// buildSceneContext 构建场景生成上下文
func (h *AIHandler) buildSceneContext(req GenerateSceneRequest) map[string]interface{} {
	// 如果提供了旧格式的context，合并使用
	context := make(map[string]interface{})
	if req.Context != nil {
		for k, v := range req.Context {
			context[k] = v
		}
	}

	// 添加新格式的上下文信息
	if req.WorldID != "" {
		context["world_id"] = req.WorldID
	}
	if req.SceneName != "" {
		context["scene_name"] = req.SceneName
	}
	if req.SceneType != "" {
		context["scene_type"] = req.SceneType
	}
	if req.Theme != "" {
		context["theme"] = req.Theme
	}
	if req.Mood != "" {
		context["mood"] = req.Mood
	}
	if len(req.ConnectedScenes) > 0 {
		context["connected_scenes"] = req.ConnectedScenes
	}
	if req.SpecialRequirements != "" {
		context["special_requirements"] = req.SpecialRequirements
	}

	return context
}

// GenerateCharacterRequest 角色生成请求
type GenerateCharacterRequest struct {
	Prompt  string                 `json:"prompt" binding:"required"`
	Context map[string]interface{} `json:"context"`
	WorldID *uuid.UUID             `json:"world_id"`
}

// GenerateEventRequest 事件生成请求
type GenerateEventRequest struct {
	Prompt  string                 `json:"prompt" binding:"required"`
	Context map[string]interface{} `json:"context"`
	WorldID *uuid.UUID             `json:"world_id"`
}
