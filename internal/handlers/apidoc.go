package handlers

import (
	"net/http"
	"strconv"

	"ai-text-game-iam-npc/internal/apidoc"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
)

// APIDocHandler API文档处理器
type APIDocHandler struct {
	service *apidoc.Service
	logger  logger.Logger
}

// NewAPIDocHandler 创建新的API文档处理器
func NewAPIDocHandler(service *apidoc.Service, logger logger.Logger) *APIDocHandler {
	return &APIDocHandler{
		service: service,
		logger:  logger,
	}
}

// GetOpenAPISpec 获取OpenAPI规范
// @Summary 获取OpenAPI规范
// @Description 获取完整的OpenAPI 3.0规范文档
// @Tags API文档
// @Produce json
// @Success 200 {object} apidoc.OpenAPISpec
// @Failure 500 {object} Response
// @Router /api/v1/docs/openapi [get]
func (h *APIDocHandler) GetOpenAPISpec(c *gin.Context) {
	h.logger.Info("获取OpenAPI规范")
	
	spec, err := h.service.GetOpenAPISpec(c.Request.Context())
	if err != nil {
		h.logger.Error("获取OpenAPI规范失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取API规范失败",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, spec)
}

// GetEndpoints 获取API端点列表
// @Summary 获取API端点列表
// @Description 获取所有扫描到的API端点信息
// @Tags API文档
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Param method query string false "HTTP方法过滤"
// @Param tag query string false "标签过滤"
// @Success 200 {object} Response{data=EndpointsResponse}
// @Failure 500 {object} Response
// @Router /api/v1/docs/endpoints [get]
func (h *APIDocHandler) GetEndpoints(c *gin.Context) {
	h.logger.Info("获取API端点列表")
	
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))
	method := c.Query("method")
	tag := c.Query("tag")
	
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}
	
	endpoints, err := h.service.GetEndpoints(c.Request.Context())
	if err != nil {
		h.logger.Error("获取API端点失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取API端点失败",
			Error:   err.Error(),
		})
		return
	}
	
	// 过滤端点
	filteredEndpoints := h.filterEndpoints(endpoints, method, tag)
	
	// 分页
	total := len(filteredEndpoints)
	start := (page - 1) * size
	end := start + size
	
	if start >= total {
		filteredEndpoints = []apidoc.APIEndpoint{}
	} else {
		if end > total {
			end = total
		}
		filteredEndpoints = filteredEndpoints[start:end]
	}
	
	response := EndpointsResponse{
		Endpoints: filteredEndpoints,
		Pagination: PaginationInfo{
			Page:       page,
			Size:       size,
			Total:      total,
			TotalPages: (total + size - 1) / size,
		},
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取API端点成功",
		Data:    response,
	})
}

// GetDocumentationStats 获取文档统计信息
// @Summary 获取文档统计信息
// @Description 获取API文档的统计信息，包括端点数量、方法分布等
// @Tags API文档
// @Produce json
// @Success 200 {object} Response{data=apidoc.DocumentationStats}
// @Failure 500 {object} Response
// @Router /api/v1/docs/stats [get]
func (h *APIDocHandler) GetDocumentationStats(c *gin.Context) {
	h.logger.Info("获取文档统计信息")
	
	stats, err := h.service.GetDocumentationStats(c.Request.Context())
	if err != nil {
		h.logger.Error("获取文档统计失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取文档统计失败",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取文档统计成功",
		Data:    stats,
	})
}

// RefreshDocumentation 刷新文档
// @Summary 刷新API文档
// @Description 手动触发API文档的重新生成
// @Tags API文档
// @Produce json
// @Success 200 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/docs/refresh [post]
func (h *APIDocHandler) RefreshDocumentation(c *gin.Context) {
	h.logger.Info("手动刷新API文档")
	
	if err := h.service.RefreshDocumentation(c.Request.Context()); err != nil {
		h.logger.Error("刷新文档失败", "error", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "刷新文档失败",
			Error:   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "文档刷新成功",
	})
}

// GetSwaggerUI 获取Swagger UI页面
// @Summary 获取Swagger UI
// @Description 返回Swagger UI界面用于API文档浏览
// @Tags API文档
// @Produce html
// @Success 200 {string} string "HTML页面"
// @Router /api/v1/docs/swagger [get]
func (h *APIDocHandler) GetSwaggerUI(c *gin.Context) {
	h.logger.Info("获取Swagger UI页面")
	
	// 生成Swagger UI HTML
	html := h.generateSwaggerUIHTML()
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// GetAPIDocumentation 获取API文档页面
// @Summary 获取API文档页面
// @Description 返回自定义的API文档浏览页面
// @Tags API文档
// @Produce html
// @Success 200 {string} string "HTML页面"
// @Router /api/v1/docs [get]
func (h *APIDocHandler) GetAPIDocumentation(c *gin.Context) {
	h.logger.Info("获取API文档页面")
	
	// 生成API文档HTML
	html := h.generateAPIDocHTML()
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// filterEndpoints 过滤端点
func (h *APIDocHandler) filterEndpoints(endpoints []apidoc.APIEndpoint, method, tag string) []apidoc.APIEndpoint {
	var filtered []apidoc.APIEndpoint
	
	for _, endpoint := range endpoints {
		// 方法过滤
		if method != "" && endpoint.Method != method {
			continue
		}
		
		// 标签过滤
		if tag != "" {
			found := false
			for _, t := range endpoint.Tags {
				if t == tag {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}
		
		filtered = append(filtered, endpoint)
	}
	
	return filtered
}

// generateSwaggerUIHTML 生成Swagger UI HTML
func (h *APIDocHandler) generateSwaggerUIHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/api/v1/docs/openapi',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                onComplete: function() {
                    console.log('Swagger UI 加载完成');
                },
                onFailure: function(data) {
                    console.error('Swagger UI 加载失败:', data);
                }
            });
        };
    </script>
</body>
</html>`
}

// generateAPIDocHTML 生成API文档HTML
func (h *APIDocHandler) generateAPIDocHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文本游戏 - API文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #7f8c8d;
            margin: 0;
        }
        .nav {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .nav a {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav a:hover {
            background: #2980b9;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature {
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .feature h3 {
            margin-top: 0;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI文本游戏 API文档</h1>
        <p>完整的后端API调试和文档系统</p>
    </div>
    
    <div class="nav">
        <a href="/api/v1/docs/swagger">Swagger UI</a>
        <a href="/api/v1/docs/openapi">OpenAPI规范</a>
        <a href="/api/v1/docs/endpoints">API端点列表</a>
        <a href="/api/v1/docs/stats">文档统计</a>
        <a href="/debug">API调试界面</a>
    </div>
    
    <div class="content">
        <div class="feature">
            <h3>🔍 API文档自动生成</h3>
            <p>自动扫描后端代码，提取API端点信息，生成标准化的OpenAPI 3.0规范文档。支持注释和装饰器中的文档信息提取。</p>
        </div>
        
        <div class="feature">
            <h3>🎯 可视化调试界面</h3>
            <p>基于Web的可视化API调试界面，支持动态表单生成、参数输入、请求发送和响应展示。</p>
        </div>
        
        <div class="feature">
            <h3>🚀 完整调试功能</h3>
            <p>支持所有HTTP方法的请求发送，完整的请求和响应信息展示，请求历史记录和认证支持。</p>
        </div>
        
        <div class="feature">
            <h3>📊 实时统计信息</h3>
            <p>提供API端点统计、方法分布、标签分类等详细信息，帮助了解API结构和使用情况。</p>
        </div>
    </div>
</body>
</html>`
}

// 响应结构体

// EndpointsResponse 端点列表响应
type EndpointsResponse struct {
	Endpoints  []apidoc.APIEndpoint `json:"endpoints"`
	Pagination PaginationInfo       `json:"pagination"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Page       int `json:"page"`
	Size       int `json:"size"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}
