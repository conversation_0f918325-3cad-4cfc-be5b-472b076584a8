package handlers

import (
	"net/http"
	"time"

	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// UnifiedAPIResponse 统一的API响应格式
// 兼容simple-server和main-server的响应格式
type UnifiedAPIResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp string      `json:"timestamp"`
	RequestID string      `json:"request_id"`
}

// UnifiedAIHandler 统一的AI处理器
// 这个处理器被simple-server和main-server共同使用
// 确保所有环境使用相同的业务逻辑代码路径
type UnifiedAIHandler struct {
	aiService *ai.Service
	logger    logger.Logger
}

// NewUnifiedAIHandler 创建统一AI处理器
func NewUnifiedAIHandler(aiService *ai.Service, logger logger.Logger) *UnifiedAIHandler {
	return &UnifiedAIHandler{
		aiService: aiService,
		logger:    logger,
	}
}

// GenerateScene 生成场景 - 统一的业务逻辑
// 这个方法被所有环境使用，不包含任何环境特定的分支逻辑
func (h *UnifiedAIHandler) GenerateScene(c *gin.Context) {
	h.logger.Info("开始处理场景生成请求")

	// 解析请求参数
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("请求参数解析失败", "error", err)
		c.JSON(http.StatusBadRequest, UnifiedAPIResponse{
			Success:   false,
			Error:     "请求参数错误: " + err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	// 提取和验证参数
	prompt, _ := req["prompt"].(string)
	if prompt == "" {
		prompt = "生成一个神秘的森林场景" // 默认提示词
	}

	worldIDStr, _ := req["world_id"].(string)
	context, _ := req["context"].(map[string]interface{})

	// 转换字符串ID为UUID（如果提供）
	var worldID *uuid.UUID
	if worldIDStr != "" {
		if parsedWorldID, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &parsedWorldID
		}
	}

	// 获取当前用户ID（如果在认证环境中）
	var userID *uuid.UUID
	if currentUserID, exists := auth.GetCurrentUserID(c); exists {
		userID = &currentUserID
	} else {
		// 在非认证环境中（如simple-server），使用固定的测试用户ID
		if fixedUserID, err := uuid.Parse("00000000-0000-0000-0000-000000000001"); err == nil {
			userID = &fixedUserID
		}
	}

	// 构建AI请求 - 使用标准的请求结构
	aiReq := &ai.GenerateRequest{
		Type:        "scene",
		Prompt:      prompt,
		Context:     context,
		MaxTokens:   500,
		Temperature: 0.7,
		WorldID:     func() *string { s := worldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"atmosphere":  "string",
			"connections": "object",
			"entities":    "array",
		},
	}

	h.logger.Info("调用AI服务生成场景", 
		"prompt_length", len(prompt),
		"has_world_id", worldID != nil,
		"has_user_id", userID != nil)

	// 调用AI服务 - 统一的调用路径
	// AI服务内部会根据配置决定使用真实API还是Mock数据
	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		h.logger.Error("AI服务调用失败", "error", err)
		c.JSON(http.StatusInternalServerError, UnifiedAPIResponse{
			Success:   false,
			Error:     "生成场景失败: " + err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	h.logger.Info("AI服务调用成功", 
		"token_usage", response.TokenUsage,
		"content_length", len(response.Content))

	// 构建统一的响应格式
	sceneData := map[string]interface{}{
		"content":         response.Content,
		"structured_data": response.StructuredData,
		"token_usage":     response.TokenUsage,
		"response_time":   response.ResponseTime,
	}

	// 返回统一的响应格式
	c.JSON(http.StatusOK, UnifiedAPIResponse{
		Success:   true,
		Data:      sceneData,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}

// GenerateCharacter 生成角色 - 统一的业务逻辑
func (h *UnifiedAIHandler) GenerateCharacter(c *gin.Context) {
	h.logger.Info("开始处理角色生成请求")

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, UnifiedAPIResponse{
			Success:   false,
			Error:     "请求参数错误: " + err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	prompt, _ := req["prompt"].(string)
	if prompt == "" {
		prompt = "生成一个游戏角色"
	}

	worldIDStr, _ := req["world_id"].(string)
	context, _ := req["context"].(map[string]interface{})

	var worldID *uuid.UUID
	if worldIDStr != "" {
		if parsedWorldID, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &parsedWorldID
		}
	}

	var userID *uuid.UUID
	if currentUserID, exists := auth.GetCurrentUserID(c); exists {
		userID = &currentUserID
	} else {
		if fixedUserID, err := uuid.Parse("00000000-0000-0000-0000-000000000001"); err == nil {
			userID = &fixedUserID
		}
	}

	aiReq := &ai.GenerateRequest{
		Type:        "character",
		Prompt:      prompt,
		Context:     context,
		MaxTokens:   400,
		Temperature: 0.8,
		WorldID:     func() *string { s := worldID.String(); return &s }(),
		UserID:      func() *string { s := userID.String(); return &s }(),
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
			"personality": "array",
			"skills":      "array",
			"background":  "string",
		},
	}

	response, err := h.aiService.GenerateContent(c.Request.Context(), aiReq)
	if err != nil {
		h.logger.Error("AI角色生成失败", "error", err)
		c.JSON(http.StatusInternalServerError, UnifiedAPIResponse{
			Success:   false,
			Error:     "生成角色失败: " + err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	characterData := map[string]interface{}{
		"content":         response.Content,
		"structured_data": response.StructuredData,
		"token_usage":     response.TokenUsage,
		"response_time":   response.ResponseTime,
	}

	c.JSON(http.StatusOK, UnifiedAPIResponse{
		Success:   true,
		Data:      characterData,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}

// GetInteractionHistory 获取AI交互历史 - 统一的业务逻辑
func (h *UnifiedAIHandler) GetInteractionHistory(c *gin.Context) {
	h.logger.Info("获取AI交互历史")

	// 解析查询参数
	worldIDStr := c.Query("world_id")
	var worldID *uuid.UUID
	if worldIDStr != "" {
		if parsedWorldID, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &parsedWorldID
		}
	}

	var userID *uuid.UUID
	if currentUserID, exists := auth.GetCurrentUserID(c); exists {
		userID = &currentUserID
	}

	// 调用AI服务获取历史记录
	interactions, err := h.aiService.GetInteractionHistory(worldID, userID, 50)
	if err != nil {
		h.logger.Error("获取AI交互历史失败", "error", err)
		c.JSON(http.StatusInternalServerError, UnifiedAPIResponse{
			Success:   false,
			Error:     "获取交互历史失败: " + err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	response := map[string]interface{}{
		"interactions": interactions,
		"total":        len(interactions),
		"page":         1,
		"limit":        50,
	}

	c.JSON(http.StatusOK, UnifiedAPIResponse{
		Success:   true,
		Data:      response,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}

// GetTokenUsageStats 获取Token使用统计 - 统一的业务逻辑
func (h *UnifiedAIHandler) GetTokenUsageStats(c *gin.Context) {
	h.logger.Info("获取Token使用统计")

	worldIDStr := c.Query("world_id")
	var worldID *uuid.UUID
	if worldIDStr != "" {
		if parsedWorldID, err := uuid.Parse(worldIDStr); err == nil {
			worldID = &parsedWorldID
		}
	}

	var userID *uuid.UUID
	if currentUserID, exists := auth.GetCurrentUserID(c); exists {
		userID = &currentUserID
	}

	stats, err := h.aiService.GetTokenUsageStats(worldID, userID, 30)
	if err != nil {
		h.logger.Error("获取Token使用统计失败", "error", err)
		c.JSON(http.StatusInternalServerError, UnifiedAPIResponse{
			Success:   false,
			Error:     "获取统计信息失败: " + err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: uuid.New().String(),
		})
		return
	}

	c.JSON(http.StatusOK, UnifiedAPIResponse{
		Success:   true,
		Data:      stats,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: uuid.New().String(),
	})
}
