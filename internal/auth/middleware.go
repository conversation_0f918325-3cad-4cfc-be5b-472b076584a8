package auth

import (
	"fmt"
	"net/http"
	"os"

	"ai-text-game-iam-npc/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(authService *Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开发环境跳过认证检查
		if isDevelopmentMode() {
			// 设置默认的开发用户信息
			setDevelopmentUser(c)
			c.Next()
			return
		}

		// 从Authorization header获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "missing_authorization",
				"message": "缺少Authorization header",
			})
			c.Abort()
			return
		}

		// 提取Bearer token
		token := utils.ExtractTokenFromHeader(authHeader)
		if token == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error":   "invalid_authorization_format",
				"message": "Authorization header格式错误",
			})
			c.Abort()
			return
		}

		// 验证token
		claims, err := authService.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "invalid_token",
				"message": "无效的token: " + err.Error(),
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_display_name", claims.DisplayName)
		c.Set("user_external_id", claims.ExternalID)
		c.Set("user_external_provider", claims.ExternalProvider)
		c.Set("user_game_roles", claims.GameRoles)
		c.Set("jwt_claims", claims)

		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件（不强制要求认证）
func OptionalAuthMiddleware(authService *Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		token := utils.ExtractTokenFromHeader(authHeader)
		if token == "" {
			c.Next()
			return
		}

		claims, err := authService.ValidateToken(token)
		if err != nil {
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_display_name", claims.DisplayName)
		c.Set("user_external_id", claims.ExternalID)
		c.Set("user_external_provider", claims.ExternalProvider)
		c.Set("user_game_roles", claims.GameRoles)
		c.Set("jwt_claims", claims)

		c.Next()
	}
}

// RequireRoles 角色权限中间件
func RequireRoles(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRoles, exists := c.Get("user_game_roles")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "no_user_roles",
				"message": "用户角色信息不存在",
			})
			c.Abort()
			return
		}

		userRolesList, ok := userRoles.([]string)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "invalid_user_roles",
				"message": "用户角色信息格式错误",
			})
			c.Abort()
			return
		}

		// 检查用户是否有所需角色
		hasRole := false
		for _, requiredRole := range roles {
			for _, userRole := range userRolesList {
				if userRole == requiredRole {
					hasRole = true
					break
				}
			}
			if hasRole {
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{
				"error":          "insufficient_permissions",
				"message":        "权限不足",
				"required_roles": roles,
				"user_roles":     userRolesList,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin 管理员权限中间件
func RequireAdmin() gin.HandlerFunc {
	return RequireRoles("admin")
}

// RequirePremium 高级用户权限中间件
func RequirePremium() gin.HandlerFunc {
	return RequireRoles("premium", "admin")
}

// GetCurrentUserID 从上下文获取当前用户ID
func GetCurrentUserID(c *gin.Context) (uuid.UUID, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, false
	}

	id, ok := userID.(uuid.UUID)
	return id, ok
}

// GetCurrentUserEmail 从上下文获取当前用户邮箱
func GetCurrentUserEmail(c *gin.Context) (string, bool) {
	email, exists := c.Get("user_email")
	if !exists {
		return "", false
	}

	emailStr, ok := email.(string)
	return emailStr, ok
}

// GetCurrentUserDisplayName 从上下文获取当前用户显示名称
func GetCurrentUserDisplayName(c *gin.Context) (string, bool) {
	displayName, exists := c.Get("user_display_name")
	if !exists {
		return "", false
	}

	nameStr, ok := displayName.(string)
	return nameStr, ok
}

// GetCurrentUserRoles 从上下文获取当前用户角色
func GetCurrentUserRoles(c *gin.Context) ([]string, bool) {
	roles, exists := c.Get("user_game_roles")
	if !exists {
		return nil, false
	}

	rolesList, ok := roles.([]string)
	return rolesList, ok
}

// GetJWTClaims 从上下文获取JWT声明
func GetJWTClaims(c *gin.Context) (*utils.JWTClaims, bool) {
	claims, exists := c.Get("jwt_claims")
	if !exists {
		return nil, false
	}

	jwtClaims, ok := claims.(*utils.JWTClaims)
	return jwtClaims, ok
}

// IsAuthenticated 检查用户是否已认证
func IsAuthenticated(c *gin.Context) bool {
	_, exists := c.Get("user_id")
	return exists
}

// HasRole 检查用户是否有指定角色
func HasRole(c *gin.Context, role string) bool {
	roles, exists := GetCurrentUserRoles(c)
	if !exists {
		return false
	}

	for _, userRole := range roles {
		if userRole == role {
			return true
		}
	}
	return false
}

// IsAdmin 检查用户是否为管理员
func IsAdmin(c *gin.Context) bool {
	return HasRole(c, "admin")
}

// IsPremium 检查用户是否为高级用户
func IsPremium(c *gin.Context) bool {
	return HasRole(c, "premium") || HasRole(c, "admin")
}

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 允许的域名列表（生产环境应该配置具体域名）
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:8080",
			"https://your-frontend-domain.com",
		}

		// 检查是否为允许的域名
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware 简单的速率限制中间件
func RateLimitMiddleware() gin.HandlerFunc {
	// 这里可以集成更复杂的速率限制库，如 golang.org/x/time/rate
	return func(c *gin.Context) {
		// 简单实现：基于IP的速率限制
		// 生产环境建议使用Redis或其他存储来实现分布式速率限制
		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开发环境放宽安全策略
		if isDevelopmentMode() {
			c.Header("X-Content-Type-Options", "nosniff")
			c.Header("X-Frame-Options", "SAMEORIGIN") // 开发环境允许同源嵌入
			c.Header("X-XSS-Protection", "1; mode=block")
			// 开发环境不设置HSTS
			c.Header("Content-Security-Policy", "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:") // 开发环境放宽CSP
		} else {
			c.Header("X-Content-Type-Options", "nosniff")
			c.Header("X-Frame-Options", "DENY")
			c.Header("X-XSS-Protection", "1; mode=block")
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
			c.Header("Content-Security-Policy", "default-src 'self'")
		}
		c.Next()
	}
}

// LoggingMiddleware 日志中间件
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		authMode := "生产模式"
		if isDevelopmentMode() {
			authMode = "开发模式(认证已跳过)"
		}
		return fmt.Sprintf("[%s] %s %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			authMode,
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
		)
	})
}

// isDevelopmentMode 检查是否为开发模式
func isDevelopmentMode() bool {
	env := os.Getenv("ENVIRONMENT")
	skipAuth := os.Getenv("SKIP_AUTH")
	return env == "development" || skipAuth == "true"
}

// setDevelopmentUser 设置开发环境默认用户信息
func setDevelopmentUser(c *gin.Context) {
	// 生成固定的开发用户ID
	devUserID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	// 设置开发用户信息到上下文
	c.Set("user_id", devUserID)
	c.Set("user_email", "<EMAIL>")
	c.Set("user_display_name", "开发测试用户")
	c.Set("user_external_id", "dev-user-001")
	c.Set("user_external_provider", "development")
	c.Set("user_game_roles", []string{"admin", "premium", "user"}) // 开发环境给予所有权限

	// 创建模拟的JWT声明
	mockClaims := &utils.JWTClaims{
		UserID:           devUserID,
		Email:            "<EMAIL>",
		DisplayName:      "开发测试用户",
		ExternalID:       "dev-user-001",
		ExternalProvider: "development",
		GameRoles:        []string{"admin", "premium", "user"},
	}
	c.Set("jwt_claims", mockClaims)

	// 添加开发模式标识
	c.Set("dev_mode", true)
}

// DevModeMiddleware 开发模式中间件，用于标识和记录开发环境请求
func DevModeMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if isDevelopmentMode() {
			c.Header("X-Dev-Mode", "true")
			c.Header("X-Auth-Bypass", "enabled")
		}
		c.Next()
	}
}
