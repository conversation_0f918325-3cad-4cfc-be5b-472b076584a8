{"openapi": "3.0.3", "info": {"title": "AI文本游戏API", "description": "AI文本游戏后端API调试系统", "version": "1.0.0", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": ""}}, "servers": [{"url": "http://localhost:8080", "description": "开发环境服务器"}], "paths": {"/api/health": {"get": {"tags": ["Api"], "operationId": "get_api_health", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/:character_id/actions": {"post": {"tags": ["Game"], "summary": "执行角色行动", "description": "角色执行指定的行动，如移动、交互、说话等", "operationId": "post_api_v1_:character_id_actions", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "行动请求", "content": {"application/json": {"schema": {"type": "object", "title": "PerformActionRequest", "description": "复杂对象类型: PerformActionRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ActionResult", "description": "数据对象: ActionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/:event_id/process": {"post": {"tags": ["Game"], "summary": "处理事件", "description": "处理指定的事件", "operationId": "post_api_v1_:event_id_process", "parameters": [{"name": "event_id", "in": "path", "required": true, "description": "事件ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/:provider/callback": {"get": {"tags": ["认证"], "summary": "处理OAuth回调", "description": "处理OAuth认证回调，完成用户登录", "operationId": "get_api_v1_:provider_callback", "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "code", "in": "query", "required": true, "description": "授权码", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "user": {"type": "object", "title": "models.User", "description": "对象类型: models.User"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}}}, "/api/v1/:world_id/time-rate": {"get": {"tags": ["TimeSchedule"], "summary": "获取世界当前的时间速率", "description": "获取指定世界当前生效的时间速率", "operationId": "get_api_v1_:world_id_time-rate", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/:world_id/time-schedule": {"get": {"tags": ["TimeSchedule"], "summary": "获取世界的时间段配置", "description": "获取指定世界的时间段配置信息", "operationId": "get_api_v1_:world_id_time-schedule", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.TimeScheduleConfig", "description": "数据对象: models.TimeScheduleConfig"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["TimeSchedule"], "summary": "更新世界的时间段配置", "description": "更新指定世界的时间段配置，支持热更新", "operationId": "put_api_v1_:world_id_time-schedule", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/:world_id/time-schedule/default": {"post": {"tags": ["TimeSchedule"], "summary": "为世界创建默认时间段配置", "description": "为指定世界创建默认的时间段配置", "operationId": "post_api_v1_:world_id_time-schedule_default", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/:world_id/time-schedule/disable": {"post": {"tags": ["TimeSchedule"], "summary": "禁用世界的时间段配置", "description": "禁用指定世界的时间段配置功能", "operationId": "post_api_v1_:world_id_time-schedule_disable", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/:world_id/time-schedule/enable": {"post": {"tags": ["TimeSchedule"], "summary": "启用世界的时间段配置", "description": "启用指定世界的时间段配置功能", "operationId": "post_api_v1_:world_id_time-schedule_enable", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/:world_id/time-schedule/reset": {"post": {"tags": ["TimeSchedule"], "summary": "重置世界的时间段配置", "description": "将指定世界的时间段配置重置为默认值", "operationId": "post_api_v1_:world_id_time-schedule_reset", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/:character_id/traits": {"post": {"tags": ["Game"], "summary": "添加角色特质", "description": "为指定角色添加新的特质", "operationId": "post_api_v1_ai_:character_id_traits", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "添加特质请求", "content": {"application/json": {"schema": {"type": "object", "title": "AddTraitRequest", "description": "复杂对象类型: AddTraitRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/generate": {"post": {"tags": ["AI"], "summary": "生成AI内容", "description": "使用AI生成游戏内容，如场景、角色、事件等", "operationId": "post_api_v1_ai_generate", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/generate/character": {"post": {"tags": ["AI"], "summary": "生成游戏角色", "description": "根据提示词生成游戏角色描述和属性", "operationId": "post_api_v1_ai_generate_character", "requestBody": {"description": "角色生成请求", "content": {"application/json": {"schema": {"type": "object", "title": "GenerateCharacterRequest", "description": "复杂对象类型: GenerateCharacterRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/generate/event": {"post": {"tags": ["AI"], "summary": "生成游戏事件", "description": "根据提示词生成游戏事件描述和效果", "operationId": "post_api_v1_ai_generate_event", "requestBody": {"description": "事件生成请求", "content": {"application/json": {"schema": {"type": "object", "title": "GenerateEventRequest", "description": "复杂对象类型: GenerateEventRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/generate/scene": {"post": {"tags": ["AI"], "summary": "生成游戏场景", "description": "根据场景参数生成游戏场景描述和属性", "operationId": "post_api_v1_ai_generate_scene", "requestBody": {"description": "场景生成请求", "content": {"application/json": {"schema": {"type": "object", "title": "GenerateSceneRequest", "description": "复杂对象类型: GenerateSceneRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/history": {"get": {"tags": ["AI"], "summary": "获取AI交互历史", "description": "获取用户或世界的AI交互历史记录", "operationId": "get_api_v1_ai_history", "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "limit", "in": "query", "required": false, "description": "限制数量", "example": "50", "schema": {"type": "integer", "format": "int32", "example": "50"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "[]models.AIInteraction", "description": "数据对象: []models.AIInteraction"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/ai/stats": {"get": {"tags": ["AI"], "summary": "获取token使用统计", "description": "获取用户或世界的token使用统计信息", "operationId": "get_api_v1_ai_stats", "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "days", "in": "query", "required": false, "description": "统计天数", "example": "30", "schema": {"type": "integer", "format": "int32", "example": "30"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "object", "description": "数据对象: object"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/auth/:provider/url": {"get": {"tags": ["认证"], "summary": "获取OAuth认证URL", "description": "获取指定提供商的OAuth认证URL", "operationId": "get_api_v1_auth_:provider_url", "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"auth_url": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}}}, "/api/v1/auth/auth/login": {"post": {"tags": ["Api"], "operationId": "post_api_v1_auth_auth_login", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/auth/logout": {"post": {"tags": ["认证"], "summary": "用户登出", "description": "用户登出（客户端需要删除本地token）", "operationId": "post_api_v1_auth_logout", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/batch-validate": {"post": {"tags": ["Validation"], "summary": "批量校验内容", "description": "批量校验多个内容项", "operationId": "post_api_v1_batch-validate", "requestBody": {"description": "校验内容请求", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "object", "title": "BatchValidateContentRequest", "description": "复杂对象类型: BatchValidateContentRequest"}}, "required": ["request", "request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "BatchValidationResult", "description": "数据对象: BatchValidationResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/database/info": {"get": {"tags": ["Api"], "operationId": "get_api_v1_database_info", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/:character_id": {"get": {"tags": ["Game"], "summary": "获取角色信息", "description": "获取指定角色的详细信息", "operationId": "get_api_v1_game_:character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Character", "description": "数据对象: models.Character"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["Game"], "summary": "更新角色信息", "description": "更新指定角色的基本信息", "operationId": "put_api_v1_game_:character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "更新角色请求", "content": {"application/json": {"schema": {"type": "object", "title": "UpdateCharacterRequest", "description": "复杂对象类型: UpdateCharacterRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "delete": {"tags": ["Game"], "summary": "删除角色", "description": "删除指定的角色", "operationId": "delete_api_v1_game_:character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/experiences": {"post": {"tags": ["Game"], "summary": "添加角色阅历", "description": "为指定角色添加新的阅历", "operationId": "post_api_v1_game_:character_id_experiences", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "添加阅历请求", "content": {"application/json": {"schema": {"type": "object", "title": "AddExperienceRequest", "description": "复杂对象类型: AddExperienceRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/interact/:target_character_id": {"post": {"tags": ["Game"], "summary": "与角色交互", "description": "角色与另一个角色进行交互", "operationId": "post_api_v1_game_:character_id_interact_:target_character_id", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "target_character_id", "in": "path", "required": true, "description": "目标角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "交互请求", "content": {"application/json": {"schema": {"type": "object", "title": "InteractionRequest", "description": "复杂对象类型: InteractionRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "InteractionResult", "description": "数据对象: InteractionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/memories": {"post": {"tags": ["Game"], "summary": "添加角色记忆", "description": "为指定角色添加新的记忆", "operationId": "post_api_v1_game_:character_id_memories", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "添加记忆请求", "content": {"application/json": {"schema": {"type": "object", "title": "AddMemoryRequest", "description": "复杂对象类型: AddMemoryRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/move": {"post": {"tags": ["Game"], "summary": "移动角色", "description": "移动角色到指定场景", "operationId": "post_api_v1_game_:character_id_move", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "移动角色请求", "content": {"application/json": {"schema": {"type": "object", "title": "MoveCharacterRequest", "description": "复杂对象类型: MoveCharacterRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:character_id/speak": {"post": {"tags": ["Game"], "summary": "在场景中说话", "description": "角色在当前场景中说话，其他角色可以听到", "operationId": "post_api_v1_game_:character_id_speak", "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "说话请求", "content": {"application/json": {"schema": {"type": "object", "title": "SpeakRequest", "description": "复杂对象类型: SpeakRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "SpeechResult", "description": "数据对象: SpeechResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:id": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_:id", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/:id/config": {"put": {"tags": ["Api"], "operationId": "put_api_v1_game_:id_config", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/:scene_id": {"get": {"tags": ["Game"], "summary": "获取场景信息", "description": "获取指定场景的详细信息", "operationId": "get_api_v1_game_:scene_id", "parameters": [{"name": "scene_id", "in": "path", "required": true, "description": "场景ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Scene", "description": "数据对象: models.Scene"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id": {"get": {"tags": ["Game"], "summary": "获取世界信息", "description": "获取指定世界的详细信息", "operationId": "get_api_v1_game_:world_id", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.World", "description": "数据对象: models.World"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["Game"], "summary": "更新世界信息", "description": "更新指定世界的信息", "operationId": "put_api_v1_game_:world_id", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "更新世界请求", "content": {"application/json": {"schema": {"type": "object", "title": "UpdateWorldRequest", "description": "复杂对象类型: UpdateWorldRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "delete": {"tags": ["Game"], "summary": "删除世界", "description": "删除指定的世界", "operationId": "delete_api_v1_game_:world_id", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/characters": {"get": {"tags": ["Game"], "summary": "获取世界中的角色列表", "description": "获取指定世界中的角色列表，支持按角色类型筛选", "operationId": "get_api_v1_game_:world_id_characters", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}, {"name": "character_type", "in": "query", "required": false, "description": "角色类型", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/join": {"post": {"tags": ["Game"], "summary": "加入世界", "description": "用户加入指定的世界", "operationId": "post_api_v1_game_:world_id_join", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/leave": {"post": {"tags": ["Game"], "summary": "离开世界", "description": "用户离开指定的世界", "operationId": "post_api_v1_game_:world_id_leave", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/state": {"get": {"tags": ["Game"], "summary": "获取世界状态", "description": "获取指定世界的当前状态信息", "operationId": "get_api_v1_game_:world_id_state", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "game.GameState", "description": "数据对象: game.GameState"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/tick": {"post": {"tags": ["Game"], "summary": "处理世界时钟周期", "description": "执行世界的一个时钟周期，包括时间推进、NPC行为、环境事件等", "operationId": "post_api_v1_game_:world_id_tick", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/:world_id/time": {"post": {"tags": ["Game"], "summary": "更新世界信息", "description": "更新指定世界的信息", "operationId": "post_api_v1_game_:world_id_time", "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "requestBody": {"description": "时间更新请求", "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "object", "title": "UpdateWorldRequest", "description": "复杂对象类型: UpdateWorldRequest"}}, "required": ["request", "request"]}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/characters": {"post": {"tags": ["Api"], "operationId": "post_api_v1_game_characters", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/characters/:id": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_characters_:id", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/games/:worldId/actions": {"post": {"tags": ["Api"], "operationId": "post_api_v1_game_games_:worldId_actions", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/games/:worldId/status": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_games_:worldId_status", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/generate": {"post": {"tags": ["Game"], "summary": "AI生成世界配置", "description": "使用AI生成多个候选世界配置供用户选择", "operationId": "post_api_v1_game_generate", "requestBody": {"description": "生成世界配置请求", "content": {"application/json": {"schema": {"type": "object", "title": "GenerateWorldsRequest", "description": "复杂对象类型: GenerateWorldsRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "GenerateWorldsResponse", "description": "数据对象: GenerateWorldsResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/game/my-characters": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_my-characters", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/my-worlds": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_my-worlds", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/public-worlds": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_public-worlds", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/search": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_search", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/world/:worldId/characters": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_world_:worldId_characters", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/game/worlds": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_worlds", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}, "post": {"tags": ["世界管理"], "summary": "创建世界", "description": "创建一个新的游戏世界", "operationId": "post_api_v1_game_worlds", "requestBody": {"description": "世界创建参数", "content": {"application/json": {"schema": {"type": "object", "properties": {"description": {"type": "string", "example": "example", "description": "string类型的字段"}, "name": {"type": "string", "example": "example", "description": "string类型的字段"}, "settings": {"type": "object", "properties": {"difficulty": {"type": "string", "example": "example", "description": "string类型的字段"}, "max_players": {"type": "integer", "format": "int32", "example": 1, "description": "int类型的字段"}}, "description": "object{difficulty=string,max_players=int}类型的字段"}}}}}, "required": true}, "responses": {"200": {"description": "创建成功", "schema": {"type": "object", "title": "APIResponse", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: APIResponse"}, "examples": null}}}}, "/api/v1/game/worlds/:id": {"get": {"tags": ["Api"], "operationId": "get_api_v1_game_worlds_:id", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}, "put": {"tags": ["Api"], "operationId": "put_api_v1_game_worlds_:id", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}, "delete": {"tags": ["Api"], "operationId": "delete_api_v1_game_worlds_:id", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/health": {"get": {"tags": ["Api"], "operationId": "get_api_v1_health", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/refresh": {"post": {"tags": ["认证"], "summary": "刷新JWT token", "description": "刷新即将过期的JWT token", "operationId": "post_api_v1_refresh", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/trigger": {"post": {"tags": ["Game"], "summary": "触发事件", "description": "手动触发一个游戏事件", "operationId": "post_api_v1_trigger", "requestBody": {"description": "触发事件请求", "content": {"application/json": {"schema": {"type": "object", "title": "TriggerEventRequest", "description": "复杂对象类型: TriggerEventRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "EventResult", "description": "数据对象: EventResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/user/:id": {"get": {"tags": ["Api"], "operationId": "get_api_v1_user_:id", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/user/:id/preferences": {"put": {"tags": ["Api"], "operationId": "put_api_v1_user_:id_preferences", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/user/profile": {"get": {"tags": ["认证"], "summary": "获取当前用户资料", "description": "获取当前认证用户的详细资料信息", "operationId": "get_api_v1_user_profile", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, "put": {"tags": ["认证"], "summary": "更新用户资料", "description": "更新当前用户的资料信息", "operationId": "put_api_v1_user_profile", "requestBody": {"description": "用户资料", "content": {"application/json": {"schema": {"type": "object", "title": "UpdateProfileRequest", "description": "复杂对象类型: UpdateProfileRequest"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/user/search": {"get": {"tags": ["Api"], "operationId": "get_api_v1_user_search", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/user/user/profile": {"get": {"tags": ["Api"], "operationId": "get_api_v1_user_user_profile", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "400": {"description": "请求错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}, "500": {"description": "服务器错误", "schema": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "examples": null}}}}, "/api/v1/validate": {"post": {"tags": ["TimeSchedule"], "summary": "验证时间段配置", "description": "验证时间段配置的有效性，不保存到数据库", "operationId": "post_api_v1_validate", "requestBody": {"description": "请求数据", "content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}}, "/api/v1/validation/config": {"get": {"tags": ["Validation"], "summary": "获取校验配置", "description": "获取当前的内容校验配置信息", "operationId": "get_api_v1_validation_config", "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ValidationConfigResponse", "description": "数据对象: ValidationConfigResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}}}}}, "components": {"schemas": {"Error": {"type": "object", "properties": {"code": {"type": "string", "description": "错误代码"}, "details": {"type": "object", "description": "错误详情"}, "message": {"type": "string", "description": "错误消息"}}, "required": ["code", "message"]}, "Response": {"type": "object", "properties": {"data": {"type": "object", "description": "响应数据"}, "error": {"type": "string", "description": "错误信息"}, "message": {"type": "string", "description": "响应消息"}, "success": {"type": "boolean", "description": "请求是否成功"}, "timestamp": {"type": "string", "description": "时间戳"}}, "required": ["success"]}}, "securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key认证", "name": "X-API-Key", "in": "header"}, "BearerAuth": {"type": "http", "description": "JWT Bearer token认证", "scheme": "bearer", "bearerFormat": "JWT"}}}, "tags": [{"name": "Api", "description": "Api相关API"}, {"name": "认证", "description": "认证相关API"}, {"name": "Validation", "description": "Validation相关API"}, {"name": "AI", "description": "AI相关API"}, {"name": "Game", "description": "Game相关API"}, {"name": "TimeSchedule", "description": "TimeSchedule相关API"}, {"name": "世界管理", "description": "世界管理相关API"}]}